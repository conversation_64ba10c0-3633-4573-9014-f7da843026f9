<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="LockedOrientationActivity">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission
        android:name="com.google.android.gms.permission.AD_ID"
        tools:node="remove" />

    <queries>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
        </intent>
    </queries>

    <application
        android:name=".LoveBeatApplication"
        android:allowBackup="false"
        android:fullBackupContent="false"
        android:icon="@drawable/logo_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".ui.onboarding.PledgeActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.favorites.ProfileSongSelectionActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.onboarding.ActorFavoriteActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.ActressFavoriteActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.MusicianFavoriteActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.home.chat.message.ChatSongSelectionActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.onboarding.SingerFavoriteActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.favorites.FavoritesActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.favorites.MovieListActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.favorites.MusicListActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.favorites.FavoritesSettingsActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.onboarding.genderFlow.WhoShouldSeeActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.genderFlow.GenderListActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.genderFlow.InterestedInActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.genderFlow.IdentifyYourselfActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".subscriptions.views.SubscriptionPlusCelebrationActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.home.settings.AddNewCityActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />

                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="lovebeatsapis.co"
                    android:pathPrefix="/link"
                    android:scheme="https" />
            </intent-filter>
            <!-- Branch URI Scheme -->
            <intent-filter>
                <data
                    android:host="open"
                    android:scheme="lovebeats" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- Branch App Links -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="lovebeats.app.link" />
                <data android:scheme="https" />
                <data android:host="lovebeats-alternate.app.link" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="lovebeats.test-app.link" />
                <data android:scheme="https" />
                <data android:host="lovebeats-alternate.test-app.link" />
            </intent-filter>
        </activity>
        <activity
            android:name=".subscriptions.views.SubscriptionActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.home.settings.SettingsActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.paidVersion.likesYou.LikesYouActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.paidVersion.likesYou.UpgradeActivity"
            android:exported="true"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="OPEN_UPGRADE_ACTIVITY" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.home.chat.ChatActivity"
            android:exported="true"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="OPEN_CHAT_ACTIVITY" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.home.browseProfiles.BrowseProfilesActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.home.BottomNavigationBarActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.home.browseProfiles.OtherUserProfileModal"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.home.browseProfiles.MutualMatchActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.home.settings.UpdatePhoneNumberOtpActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.home.settings.UpdatePhoneNumberActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.home.settings.UpdateEmailActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.home.chat.message.MessageListActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.home.settings.SettingsIceBreakerListActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.home.settings.SettingsIceBreakerTitleActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.home.settings.EditProfileModalActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.onboarding.AgerestrictionActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.login.Intro1Activity"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.login.PhoneNumberActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateAlwaysVisible" />
        <activity
            android:name=".ui.login.CodeVerificationActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateAlwaysVisible" />
        <activity
            android:name=".ui.onboarding.EmailActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.onboarding.EnableLocationActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.FirstNameActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.onboarding.DateOfBirthActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.onboarding.SexPreferenceActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.YourHeightActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.onboarding.PhotoUploadActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.ProfessionActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.onboarding.Education1Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.onboarding.HometownActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.onboarding.Icebreaker1Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.Icebreaker2Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.onboarding.Icebreaker3Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.onboarding.Icebreaker4Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".ui.blanks.BlankNoNetworkConnectionActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.contentprovider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Trigger Google Play services to install the backported photo picker module. -->
        <!--suppress AndroidDomInspection -->
        <service android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false"
            tools:ignore="MissingClass">
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>
            <meta-data android:name="photopicker_activity:0:required" android:value="" />
        </service>

        <service
            android:name=".firebase.fcm.FirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name=".services.BackgroundLocationIntentService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/logo_launcher" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/color_primary" /> <!-- Branch init -->
        <meta-data
            android:name="io.branch.sdk.BranchKey"
            android:value="key_live_mwpReepzf4hvvd2mWJYtcidptDb7U518" />
        <meta-data
            android:name="io.branch.sdk.BranchKey.test"
            android:value="key_test_aycLnajFk5bFqpXe7Q8zpambBxmWN29L" />
        <meta-data
            android:name="io.branch.sdk.TestMode"
            android:value="false" />

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
    </application>

</manifest>