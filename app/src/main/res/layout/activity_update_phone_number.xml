<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:focusableInTouchMode="true"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/edit_profile1_activity_nav_bar_constraint_layout_height"
        android:layout_marginRight="@dimen/edit_profile1_activity_nav_bar_constraint_layout_margin_end"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <TextView
            android:id="@+id/title_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="Update phone number"
            android:textColor="@color/grey1"
            android:textSize="@dimen/edit_profile1_activity_title_text_view_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/preferences_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/email_activity_button_constraint_layout_height"
        android:layout_marginLeft="@dimen/email_activity_button_constraint_layout_margin_start"
        android:layout_marginRight="@dimen/email_activity_button_constraint_layout_margin_end"
        android:layout_marginTop="@dimen/email_activity_button_constraint_layout_margin_top"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout"
        tools:layout_editor_absoluteX="24dp"
        tools:layout_editor_absoluteY="292dp">

        <android.widget.Button
            android:id="@+id/button_enable_location"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/email_activity_button_large_active_button_height"
            android:layout_marginEnd="@dimen/email_activity_button_large_active_button_margin_end"
            android:background="@drawable/bottom_button_disabled_state"
            android:enabled="false"
            android:text="@string/next"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="0dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/phone_number_activity_group_constraint_layout_margin_start"
        android:layout_marginTop="24dp"
        android:layout_marginRight="@dimen/phone_number_activity_group_constraint_layout_margin_end"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nav_bar_constraint_layout">

        <TextView
            android:id="@+id/we_ll_text_you_acod_text_view"
            android:layout_width="@dimen/phone_number_activity_we_ll_text_you_acod_text_view_width"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:fontFamily="@font/inter"
            android:gravity="left"
            android:lineSpacingMultiplier="1.09"
            android:text="Enter your new phone number."
            android:textColor="@color/grey1"
            android:textSize="@dimen/phone_number_activity_we_ll_text_you_acod_text_view_text_size"
            app:layout_constraintBottom_toTopOf="@+id/phone_number_edit_text"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <TextView
            android:id="@+id/height_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/phone_number_activity_text_view_text_view_margin_bottom"
            android:fontFamily="@font/inter"
            android:gravity="left"
            android:lineSpacingMultiplier="1.09"
            android:text="@string/ph_no_country_code"
            android:textColor="@color/grey1"
            android:textSize="@dimen/phone_number_activity_text_view_text_view_text_size"
            app:layout_constraintBottom_toTopOf="@+id/rectangle_constraint_layout"
            app:layout_constraintLeft_toLeftOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="146dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rectangle_constraint_layout"
            android:layout_width="@dimen/phone_number_activity_rectangle_constraint_layout_width"
            android:layout_height="@dimen/phone_number_activity_rectangle_constraint_layout_height"
            android:layout_marginBottom="@dimen/phone_number_activity_rectangle_constraint_layout_margin_bottom"
            android:background="@color/grey2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="175dp" />

        <EditText
            android:id="@+id/phone_number_edit_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/phone_number_activity_e_g5555555555_edit_text_margin_start"
            android:layout_marginBottom="@dimen/phone_number_activity_e_g5555555555_edit_text_margin_bottom"
            android:background="@color/white"
            android:fontFamily="@font/inter"
            android:gravity="start"
            android:hint="@string/ph_no_hint_text"
            android:inputType="phone"
            android:lineSpacingMultiplier="1"
            android:maxLength="10"
            android:textColor="@color/grey1"
            android:textColorHint="@color/grey4"
            android:textSize="@dimen/phone_number_activity_e_g5555555555_edit_text_text_size"
            app:layout_constraintBottom_toTopOf="@+id/rectangle_two_constraint_layout"
            app:layout_constraintLeft_toRightOf="@+id/height_text_view"
            android:importantForAutofill="no" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rectangle_two_constraint_layout"
            android:layout_width="@dimen/phone_number_activity_rectangle_two_constraint_layout_width"
            android:layout_height="@dimen/phone_number_activity_rectangle_two_constraint_layout_height"
            android:layout_marginLeft="@dimen/phone_number_activity_rectangle_two_constraint_layout_margin_start"
            android:layout_marginBottom="@dimen/phone_number_activity_rectangle_two_constraint_layout_margin_bottom"
            android:background="@color/grey2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/rectangle_constraint_layout"
            tools:layout_editor_absoluteX="46dp"
            tools:layout_editor_absoluteY="175dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>