<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <nl.dionsegijn.konfetti.KonfettiView
        android:id="@+id/viewKonfetti"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:elevation="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group4_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="72dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/height_celebration_members_image_view"
        app:layout_constraintVertical_bias="0">

        <TextView
            android:id="@+id/congrats_you_re_amtext_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="12dp"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.29"
            android:text="@string/subscription_celebration_welcome_text"
            android:textColor="@color/grey1"
            android:textSize="22sp"
            app:layout_constraintBottom_toTopOf="@+id/men_over61_and_wo_text_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/men_over61_and_wo_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="36dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="36dp"
            android:layout_marginBottom="72dp"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="@string/subscription_celebration_desc"
            android:textColor="@color/grey2"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/congrats_you_re_amtext_view"
            app:layout_constraintVertical_bias="0" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/height_celebration_members_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="84dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toTopOf="@+id/group4_constraint_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1"
        app:srcCompat="@drawable/hand" />

    <ImageView
        android:id="@+id/subscription_point_direct_messaging_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="28dp"
        android:scaleType="centerCrop"
        app:srcCompat="@drawable/ic_direct_messaging_point"
        app:layout_constraintEnd_toStartOf="@+id/subscription_point_direct_messaging"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/group4_constraint_layout" />

    <TextView
        android:id="@+id/subscription_point_direct_messaging"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="26dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="start"
        android:lineSpacingMultiplier="1.09"
        android:text="Direct Messaging"
        android:textColor="@color/grey1"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/subscription_point_direct_messaging_image"
        app:layout_constraintTop_toBottomOf="@+id/group4_constraint_layout" />

    <ImageView
        android:id="@+id/subscription_point_view_passed_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:scaleType="centerCrop"
        app:srcCompat="@drawable/ic_view_passed_profiles_point"
        app:layout_constraintEnd_toStartOf="@+id/subscription_point_view_passed"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/subscription_point_direct_messaging_image"
        app:layout_constraintTop_toBottomOf="@+id/subscription_point_direct_messaging" />

    <TextView
        android:id="@+id/subscription_point_view_passed"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="start"
        android:lineSpacingMultiplier="1.09"
        android:text="View Passed Profiles"
        android:textColor="@color/grey1"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/subscription_point_view_passed_image"
        app:layout_constraintTop_toBottomOf="@+id/subscription_point_direct_messaging" />

    <ImageView
        android:id="@+id/subscription_point1_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:scaleType="centerCrop"
        app:srcCompat="@drawable/ic_welcome_subscription_point1"
        app:layout_constraintEnd_toStartOf="@+id/subscription_point1"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/subscription_point_direct_messaging_image"
        app:layout_constraintTop_toBottomOf="@+id/subscription_point_view_passed" />

    <TextView
        android:id="@+id/subscription_point1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="start"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/subscription_celebration_point_1"
        android:textColor="@color/grey1"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/subscription_point1_image"
        app:layout_constraintTop_toBottomOf="@+id/subscription_point_view_passed" />

    <ImageView
        android:id="@+id/subscription_point2_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:scaleType="centerCrop"
        app:srcCompat="@drawable/ic_welcome_subscription_point2"
        app:layout_constraintEnd_toStartOf="@+id/subscription_point2"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/subscription_point1_image"
        app:layout_constraintTop_toBottomOf="@+id/subscription_point1" />

    <TextView
        android:id="@+id/subscription_point2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="start"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/subscription_celebration_point_2"
        android:textColor="@color/grey1"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/subscription_point2_image"
        app:layout_constraintTop_toBottomOf="@+id/subscription_point1" />

    <ImageView
        android:id="@+id/subscription_point3_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:scaleType="centerCrop"
        app:srcCompat="@drawable/ic_welcome_subscription_point3"
        app:layout_constraintEnd_toStartOf="@+id/subscription_point3"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/subscription_point1_image"
        app:layout_constraintTop_toBottomOf="@+id/subscription_point2" />

    <TextView
        android:id="@+id/subscription_point3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="start"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/subscription_celebration_point_3"
        android:textColor="@color/grey1"
        android:textSize="16sp"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/subscription_point3_image"
        app:layout_constraintTop_toBottomOf="@+id/subscription_point2" />

    <android.widget.Button
        android:id="@+id/button_large_active_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_celebration1_activity_button_large_active_button_height"
        android:layout_marginStart="48dp"
        android:layout_marginTop="54dp"
        android:layout_marginEnd="48dp"
        android:background="@drawable/bottom_button_active_state_ripple"
        android:text="@string/got_it"
        android:textColor="@color/white"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/subscription_point3" />
</androidx.constraintlayout.widget.ConstraintLayout>