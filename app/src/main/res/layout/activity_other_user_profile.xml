<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_other_user_browse_profiles"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="ContentDescription"
    android:background="@color/white">

    <ScrollView
        android:id="@+id/view_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingBottom="16dp"
        android:clipToPadding="false"
        app:layout_constraintBottom_toTopOf="@+id/overlay_barrier"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/group3_constraint_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/profile_men_seactivity_group3_constraint_layout_margin_start"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="20dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/genres_view_recycler_view">

                <TextView
                    android:id="@+id/profession_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_profession_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/profile_men_seactivity_profession_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_profession_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/user_height1_text_view"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="63dp" />

                <TextView
                    android:id="@+id/user_profession_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_product_designer_text_view_margin_top"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text=""
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_product_designer_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/profession_text_view" />

                <TextView
                    android:id="@+id/age_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_age_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/profile_men_seactivity_height_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_age_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="-1dp" />

                <TextView
                    android:id="@+id/user_location_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="end"
                    android:lineSpacingMultiplier="1.1"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_age_text_view_text_size"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <ImageView
                    android:id="@+id/location_image_view"
                    android:layout_width="12dp"
                    android:layout_height="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="4dp"
                    android:gravity="end"
                    android:scaleType="fitXY"
                    android:src="@drawable/ic_location_icon"
                    app:layout_constraintEnd_toStartOf="@+id/user_location_text_view"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/color_primary" />

                <TextView
                    android:id="@+id/distance_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter"
                    android:gravity="end"
                    android:layout_marginTop="2dp"
                    android:lineSpacingMultiplier="1.09"
                    android:textColor="@color/grey2"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/location_image_view" />

                <TextView
                    android:id="@+id/user_height1_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_yrs_text_view_margin_top"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text=""
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_yrs_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/age_text_view"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="22dp" />

                <TextView
                    android:id="@+id/user_education_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_university_of_new_ha_text_view_margin_top"
                    android:layout_marginEnd="18dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text=""
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_university_of_new_ha_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/education_text_view" />

                <TextView
                    android:id="@+id/user_home_town_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text=""
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_san_mateo_ca_text_view_text_size"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/hometown_text_view" />

                <TextView
                    android:id="@+id/education_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_education_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/profile_men_seactivity_education_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_education_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/user_profession_text_view"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="126dp" />

                <TextView
                    android:id="@+id/hometown_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/profile_men_seactivity_hometown_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_hometown_text_view_text_size"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/user_education_text_view" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/group10_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="@dimen/profile_men_seactivity_group10_constraint_layout_height"
                android:layout_marginStart="@dimen/profile_men_seactivity_group10_constraint_layout_margin_start"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:layout_marginEnd="@dimen/profile_men_seactivity_group10_constraint_layout_margin_end"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_second_photo_image_view">

                <ImageView
                    android:id="@+id/boots_bottom_image_view"
                    android:layout_width="108dp"
                    android:layout_height="32dp"
                    android:elevation="5dp"
                    android:scaleType="fitXY"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="10dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/user_match_boot_preview_text_view"
                    android:layout_width="@dimen/profile_men_seactivity_you_can_wear_up_to2_text_view_width"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/profile_men_seactivity_you_can_wear_up_to2_text_view_margin_start"
                    android:layout_marginTop="@dimen/profile_men_seactivity_you_can_wear_up_to2_text_view_margin_top"
                    android:layout_marginEnd="@dimen/profile_men_seactivity_you_can_wear_up_to2_text_view_margin_end"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.03"
                    android:text="@string/profile_men_seactivity_you_can_wear_up_to2_text_view_text"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_you_can_wear_up_to2_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/boots_bottom_image_view"
                    tools:layout_editor_absoluteX="17dp"
                    tools:layout_editor_absoluteY="42dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/user_second_photo_image_view"
                android:layout_width="0dp"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/profilecard_blank_photo_copy_constraint_layout" />

            <ImageView
                android:id="@+id/user_third_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/profilecard_blank_photo_copy3_constraint_layout" />

            <ImageView
                android:id="@+id/user_fourth_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_third_photo_image_view" />

            <ImageView
                android:id="@+id/user_fifth_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_fourth_photo_image_view" />

            <ImageView
                android:id="@+id/user_sixth_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_fifth_photo_image_view" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/profilecard_blank_photo_copy_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/group3_constraint_layout">

                <ImageView
                    android:id="@+id/rectangle_image_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/grey5"
                    android:scaleType="centerCrop"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:background="@color/grey5"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/ice_breaker_1_question_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text="@string/profile_men_seactivity_how_do_you_describe_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_how_do_you_describe_text_view_text_size"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/copy_image_view" />

                <TextView
                    android:id="@+id/ice_breaker_1_answer_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginTop="4dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.03"
                    android:layout_marginBottom="26dp"
                    app:layout_constraintVertical_bias="0"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:text="@string/profile_men_seactivity_i_mdefinitely_fun_atext_view_text"
                    android:textColor="@color/grey2"
                    android:textSize="18sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ice_breaker_1_question_text_view" />

                <ImageView
                    android:id="@+id/copy_image_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/profile_men_seactivity_copy_image_view_margin_start"
                    android:layout_marginTop="24dp"
                    android:scaleType="center"
                    android:src="@drawable/ic_quote"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/profilecard_blank_photo_copy3_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/group10_constraint_layout">

                <ImageView
                    android:id="@+id/rectangle_two_image_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/grey5"
                    android:scaleType="centerCrop"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/user_second_ice_breaker_photo_image_view"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/user_second_ice_breaker_photo_image_view"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:background="@color/grey5"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/ice_breaker_2_question_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text="@string/profile_men_seactivity_how_do_you_describe_two_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_how_do_you_describe_two_text_view_text_size"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/copy_two_image_view" />

                <TextView
                    android:id="@+id/ice_breaker_2_answer_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="26dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.03"
                    android:text="@string/profile_men_seactivity_i_mdefinitely_fun_atwo_text_view_text"
                    android:textColor="@color/grey2"
                    android:textSize="18sp"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintVertical_bias="0"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ice_breaker_2_question_text_view" />

                <ImageView
                    android:id="@+id/copy_two_image_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/profile_men_seactivity_copy_two_image_view_margin_start"
                    android:layout_marginTop="24dp"
                    android:scaleType="center"
                    android:src="@drawable/ic_quote"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/user_first_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="11dp"
                android:scaleType="centerCrop"
                android:background="@color/grey5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/group5_constraint_layout" />

            <TextView
                android:id="@+id/favorites_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/edit_profile1_activity_the_basics_text_view_margin_start"
                android:layout_marginTop="24dp"
                android:fontFamily="@font/inter_bold"
                android:gravity="start"
                android:text="Favorites"
                android:textColor="@color/grey1"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_first_photo_image_view" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/favorites_view_recycler_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/favorites_text_view"
                tools:listitem="@layout/item_favorite_profile" />

            <TextView
                android:id="@+id/genres_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/edit_profile1_activity_the_basics_text_view_margin_start"
                android:layout_marginTop="24dp"
                android:fontFamily="@font/inter_bold"
                android:gravity="start"
                android:text="Genres"
                android:textColor="@color/grey1"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/favorites_view_recycler_view" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/genres_view_recycler_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/genres_text_view"
                tools:listitem="@layout/item_genres" />

            <TextView
                android:id="@+id/user_name_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/profile_men_seactivity_jon_text_view_margin_start"
                android:layout_marginTop="@dimen/profile_men_seactivity_jon_text_view_margin_top"
                android:fontFamily="@font/inter_bold"
                android:gravity="start"
                android:lineSpacingMultiplier="1.09"
                android:text=""
                android:textColor="@color/grey1"
                android:textSize="22sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:layout_editor_absoluteX="19dp"
                tools:layout_editor_absoluteY="10dp" />

            <android.widget.Button
                android:id="@+id/report_user_button"
                style="?android:attr/borderlessButtonStyle"
                android:theme="@style/BottomCTAButton"
                android:layout_width="0dp"
                android:layout_height="@dimen/profile_men_seactivity_button_ghost_button_height"
                android:layout_marginStart="24dp"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:layout_marginEnd="24dp"
                android:background="@drawable/button_border_blue"
                android:text="@string/profile_men_seactivity_button_ghost_button_text"
                android:textColor="@color/color_primary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_sixth_photo_image_view" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/group5_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="@dimen/profile_men_seactivity_group5_constraint_layout_height"
                android:layout_marginStart="@dimen/profile_men_seactivity_group5_constraint_layout_margin_start"
                android:layout_marginEnd="18dp"
                android:layout_marginTop="@dimen/profile_men_seactivity_group5_constraint_layout_margin_top"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_name_text_view">

                <TextView
                    android:id="@+id/user_age1_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:text=""
                    android:textColor="@color/grey1"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                <ImageView
                    android:id="@+id/height_verified_badge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:layout_marginStart="4dp"
                    android:layout_marginBottom="6dp"
                    android:visibility="gone"
                    android:src="@drawable/ic_height_verified_badge"
                    app:layout_constraintStart_toEndOf="@+id/user_age1_text_view"
                    app:layout_constraintBottom_toBottomOf="@+id/user_age1_text_view" />

                <TextView
                    android:id="@+id/height_verified_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:layout_marginStart="4dp"
                    android:textColor="@color/grey3"
                    android:textSize="14sp"
                    android:visibility="gone"
                    android:layout_marginBottom="6dp"
                    app:layout_constraintStart_toEndOf="@+id/height_verified_badge"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="@+id/user_age1_text_view" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/top_heels_icon"
                android:layout_width="48dp"
                android:layout_height="32dp"
                android:layout_marginEnd="18dp"
                android:layout_marginBottom="8dp"
                android:elevation="5dp"
                android:scaleType="fitXY"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/user_first_photo_image_view"
                app:srcCompat="@drawable/ic_mid_heel" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/preferences_men_secopy2supernova_activity_header_normal_constraint_layout_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <ImageButton
            android:id="@+id/edit_profile_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="18dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/title_three_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text=""
            android:textColor="@color/grey1"
            android:textSize="@dimen/preferences_men_secopy2supernova_activity_title_three_text_view_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Barrier to ensure ScrollView doesn't overlap with overlays -->
    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/overlay_barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="likes_you_user_selection_layout,chat_request_bottom_sheet,player" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/likes_you_user_selection_layout"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/player"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <android.widget.Button
            android:id="@+id/button_pass_user"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/phone_number_activity_button_large_active_button_height"
            android:background="@drawable/button_border_blue"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:text="Pass"
            android:textColor="@color/color_primary"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/button_like_user"
            app:layout_constraintHorizontal_chainStyle="spread" />

        <android.widget.Button
            android:id="@+id/button_like_user"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/phone_number_activity_button_large_active_button_height"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/bottom_button_active_state_ripple"
            android:text="Match"
            android:textColor="@color/white"
            app:layout_constraintLeft_toRightOf="@+id/button_pass_user"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/chat_request_bottom_sheet"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/player"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        <TextView
            android:id="@+id/request_sender_name_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_extrabold"
            android:text="Requested to chat:"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
        <TextView
            android:id="@+id/request_message_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:background="@color/gold_light"
            android:padding="12dp"
            android:text="Hey how are you doing?"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            app:layout_constraintTop_toBottomOf="@id/request_sender_name_text_view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
        <Button
            android:id="@+id/accept_request_button"
            android:layout_width="0dp"
            android:layout_height="@dimen/phone_number_activity_button_large_active_button_height"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="8dp"
            android:text="Accept Message"
            android:textColor="@color/white"
            android:background="@drawable/bottom_button_active_state"
            android:theme="@style/BottomCTAButton"
            style="?android:attr/borderlessButtonStyle"
            app:layout_constraintTop_toBottomOf="@id/request_message_text_view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ignore_request_button" />
        <android.widget.Button
            android:id="@+id/ignore_request_button"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/phone_number_activity_button_large_active_button_height"
            android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Ignore"
            android:textAllCaps="false"
            android:textColor="@color/color_primary"
            android:textSize="18sp"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/request_message_text_view"
            app:layout_constraintStart_toEndOf="@id/accept_request_button"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.lovebeats.customViews.PlayerView
        android:id="@+id/player"
        android:layout_width="0dp"
        android:layout_height="54dp"
        android:background="@color/color_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>