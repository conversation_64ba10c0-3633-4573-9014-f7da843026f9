<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <TextView
        android:id="@+id/should_we_show_you_gtext_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="36dp"
        android:layout_marginRight="36dp"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:lineSpacingMultiplier="0.96"
        android:text="Help get this convo off the ground by sending the first message."
        android:textColor="@color/grey2"
        android:textSize="18sp"
        app:layout_constraintBottom_toTopOf="@+id/send_message_button"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/like_text_view" />

    <android.widget.Button
        android:id="@+id/keep_browsing_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/guest_matchmaking2_activity_button_large_active_button_height"
        android:layout_marginLeft="48dp"
        android:layout_marginRight="48dp"
        android:background="@drawable/button_border_blue"
        android:text="Keep Browsing"
        android:textColor="@color/color_primary"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/send_message_button" />

    <android.widget.Button
        android:id="@+id/send_message_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/guest_matchmaking2_activity_button_large_active_two_button_height"
        android:layout_marginLeft="48dp"
        android:layout_marginTop="54dp"
        android:layout_marginRight="48dp"
        android:layout_marginBottom="@dimen/guest_matchmaking2_activity_button_large_active_two_button_margin_bottom"
        android:background="@drawable/bottom_button_active_state"
        android:text="Send Message"
        android:textColor="@color/white"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toTopOf="@+id/keep_browsing_button"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/should_we_show_you_gtext_view" />

    <ImageView
        android:id="@+id/user_profile_image_view"
        android:layout_width="155dp"
        android:layout_height="155dp"
        android:layout_marginBottom="12dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toTopOf="@+id/like_text_view"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/like_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="48dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="48dp"
        android:textColor="#25333D"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/should_we_show_you_gtext_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/user_profile_image_view" />
</androidx.constraintlayout.widget.ConstraintLayout>