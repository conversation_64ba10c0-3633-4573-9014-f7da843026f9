<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <androidx.appcompat.widget.SearchView
            android:id="@+id/search_view"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            app:iconifiedByDefault="false"
            android:background="@color/color_tertiary_lighter"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/favorites_view_recycler_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/search_view"
            tools:listitem="@layout/settings_row" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/request_favorite_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/first_name_activity_group_constraint_layout_margin_start"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            android:layout_marginEnd="@dimen/first_name_activity_group_constraint_layout_margin_end"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/search_view">

            <TextView
                android:id="@+id/header"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/first_name_activity_what_syour_first_na_text_view_margin_end"
                android:fontFamily="@font/inter_bold"
                android:gravity="left"
                android:lineSpacingMultiplier="1"
                android:singleLine="true"
                android:layout_marginTop="24dp"
                android:text="@string/didnt_find_favorite_title"
                android:textColor="@color/grey1"
                android:textSize="@dimen/first_name_activity_what_syour_first_na_text_view_text_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/sub_header"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="@dimen/first_name_activity_this_will_be_display_text_view_margin_end"
                android:fontFamily="@font/inter"
                android:gravity="left"
                android:lineSpacingMultiplier="1.09"
                android:text="@string/didnt_find_favorite_sub_title"
                android:textColor="@color/grey1"
                android:textSize="@dimen/first_name_activity_this_will_be_display_text_view_text_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/header" />

            <EditText
                android:id="@+id/favorite_editText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:layout_marginTop="24dp"
                android:hint="eg. S.S.Thaman"
                android:inputType="textNoSuggestions|textCapWords"
                android:lineSpacingMultiplier="1"
                android:textColor="@color/grey1"
                android:textColorHint="@color/grey4"
                android:textSize="@dimen/first_name_activity_value_edit_text_text_size"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/sub_header"
                android:importantForAutofill="no" />

            <android.widget.Button
                android:id="@+id/button_enable_location"
                style="?android:attr/borderlessButtonStyle"
                android:theme="@style/BottomCTAButton"
                android:layout_width="0dp"
                android:layout_height="@dimen/first_name_activity_button_large_active_button_height"
                android:background="@drawable/bottom_button_active_state"
                android:lineSpacingMultiplier="1"
                android:text="Submit"
                android:textColor="@color/white"
                android:layout_marginTop="24dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/favorite_editText" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header_normal_constraint_layout"
            android:layout_width="0dp"
            android:layout_height="@dimen/settings_settings_supernova_activity_header_normal_constraint_layout_height"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="0dp">

            <ImageButton
                android:id="@+id/account_header_left_image_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
                android:layout_marginTop="4dp"
                android:background="@null"
                android:elevation="2dp"
                android:padding="10dp"
                android:scaleType="center"
                android:src="@drawable/ic_icon_arrow_left"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_bold"
                android:gravity="center"
                android:lineSpacingMultiplier="1.09"
                android:text="Update"
                android:textColor="@color/grey1"
                android:textSize="@dimen/settings_settings_supernova_activity_title_text_view_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
