<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageView
        android:id="@+id/settings_header_left_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="12dp"
        android:background="@null"
        android:elevation="2dp"
        android:padding="10dp"
        android:scaleType="center"
        app:srcCompat="@drawable/ic_arrow_left"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/inter_bold"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:lineSpacingMultiplier="1.09"
        android:text="Add New City"
        android:textColor="@color/grey1"
        android:textSize="@dimen/settings_settings_supernova_activity_title_text_view_text_size"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/sub_title_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/inter_bold"
        android:layout_marginTop="36dp"
        android:lineSpacingMultiplier="1.09"
        android:text="Name of City or Neighborhood"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:textColor="@color/grey1"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_text_view" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/autocomplete_fragment"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintTop_toBottomOf="@+id/sub_title_text_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:name="com.google.android.libraries.places.widget.AutocompleteSupportFragment" />

</androidx.constraintlayout.widget.ConstraintLayout>