<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    >

    <TextView
        android:id="@+id/where_are_you_locate_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="center"
        android:lineSpacingMultiplier="0.97"
        android:text="@string/location_screen_title"
        android:textColor="@color/grey1"
        android:textSize="@dimen/enable_location_activity_where_are_you_locate_text_view_text_size"
        app:layout_constraintBottom_toTopOf="@+id/enable_your_location_text_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon_empty_magnifying_glass_image_view"
        app:layout_constraintVertical_bias="0.5" />

    <TextView
        android:id="@+id/enable_your_location_text_view"
        android:layout_width="@dimen/enable_location_activity_enable_your_location_text_view_width"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/location_screen_desc"
        android:textColor="@color/grey1"
        android:textSize="@dimen/enable_location_activity_enable_your_location_text_view_text_size"
        app:layout_constraintBottom_toTopOf="@+id/button_enable_location"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/where_are_you_locate_text_view"
        app:layout_constraintVertical_bias="0.5" />

    <ImageView
        android:id="@+id/icon_empty_magnifying_glass_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="36dp"
        android:src="@drawable/ic_illustration_location"
        app:layout_constraintBottom_toTopOf="@+id/where_are_you_locate_text_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.5"
        app:layout_constraintVertical_chainStyle="packed" />

    <android.widget.Button
        android:id="@+id/button_enable_location"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/enable_location_activity_button_large_active_button_height"
        android:layout_marginLeft="48dp"
        android:layout_marginTop="48dp"
        android:layout_marginRight="48dp"
        android:background="@drawable/bottom_button_active_state_ripple"
        android:text="@string/location_screen_button_action"
        android:textColor="@color/white"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/enable_your_location_text_view"
        app:layout_constraintVertical_bias="0.5" />
</androidx.constraintlayout.widget.ConstraintLayout>