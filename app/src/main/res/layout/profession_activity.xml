<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white"
    >

	<include
		layout="@layout/custom_action_bar"
		android:layout_width="match_parent"
		android:layout_height="?attr/actionBarSize" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/button_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/profession_activity_button_constraint_layout_height"
		android:layout_marginLeft="@dimen/profession_activity_button_constraint_layout_margin_start"
		android:layout_marginTop="52dp"
		android:layout_marginRight="@dimen/profession_activity_button_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout">

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/profession_activity_button_large_active_button_height"
			android:layout_marginEnd="@dimen/profession_activity_button_large_active_button_margin_end"
			android:background="@drawable/bottom_button_disabled_state"
			android:text="@string/next"
			android:textColor="@color/white"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginLeft="@dimen/profession_activity_group_constraint_layout_margin_start"
		android:layout_marginTop="64dp"
		android:layout_marginRight="@dimen/profession_activity_group_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<TextView
			android:id="@+id/what_syour_professi_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:fontFamily="@font/inter_bold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:text="@string/profession_screen_title"
			android:textColor="@color/grey1"
			android:textSize="@dimen/profession_activity_what_syour_professi_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/field_short_placeholder_constraint_layout"
			android:layout_width="0dp"
			android:layout_height="@dimen/profession_activity_field_short_placeholder_constraint_layout_height"
			android:layout_marginTop="10dp"
			android:layout_marginRight="@dimen/profession_activity_field_short_placeholder_constraint_layout_margin_end"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/what_syour_professi_text_view">

			<EditText
				android:id="@+id/profession_edit_text"
				android:layout_width="0dp"
				android:layout_height="wrap_content"
				android:layout_marginRight="@dimen/profession_activity_value_edit_text_margin_end"
				android:background="@color/white"
				android:fontFamily="@font/inter"
				android:gravity="left"
				android:hint="@string/edit_profile1_activity_value_edit_text_hint"
				android:inputType="textCapWords"
				android:lineSpacingMultiplier="1"
				android:textColor="@color/grey1"
				android:textColorHint="@color/grey4"
				android:textSize="@dimen/profession_activity_value_edit_text_text_size"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toTopOf="parent" />

			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/rectangle_constraint_layout"
				android:layout_width="0dp"
				android:layout_height="@dimen/profession_activity_rectangle_constraint_layout_height"
				android:background="@color/grey4"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				tools:layout_editor_absoluteX="0dp"
				tools:layout_editor_absoluteY="35dp" />
		</androidx.constraintlayout.widget.ConstraintLayout>
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>