<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <android.widget.Button
        android:id="@+id/button_next"
        style="?android:attr/borderlessButtonStyle"
        android:theme="@style/BottomCTAButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/first_name_activity_button_large_active_button_height"
        android:background="@drawable/bottom_button_disabled_state"
        android:enabled="false"
        android:lineSpacingMultiplier="1"
        android:text="@string/next"
        android:textColor="@color/white"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:layout_marginStart="48dp"
        android:layout_marginEnd="48dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/favorites_view_recycler_view" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/favorites_view_recycler_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/button_next"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line"
        tools:listitem="@layout/settings_row" />

    <View
        android:id="@+id/line"
        android:layout_width="0dp"
        android:layout_height="2dp"
        android:background="@color/grey5"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toBottomOf="@+id/request_favorite_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/request_favorite_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/first_name_activity_group_constraint_layout_margin_start"
        android:visibility="visible"
        android:layout_marginEnd="@dimen/first_name_activity_group_constraint_layout_margin_end"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout">

        <TextView
            android:id="@+id/header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/first_name_activity_what_syour_first_na_text_view_margin_end"
            android:fontFamily="@font/inter_bold"
            android:gravity="left"
            android:lineSpacingMultiplier="1"
            android:text="What genres of movies do you enjoy the most??"
            android:textColor="@color/grey1"
            android:textSize="24sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/sub_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="@dimen/first_name_activity_this_will_be_display_text_view_margin_end"
            android:fontFamily="@font/inter"
            android:gravity="left"
            android:lineSpacingMultiplier="1.09"
            android:text="@string/genres_sub_tile"
            android:textColor="@color/grey1"
            android:textSize="@dimen/first_name_activity_this_will_be_display_text_view_text_size"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/settings_settings_supernova_activity_header_normal_constraint_layout_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <ImageButton
            android:id="@+id/account_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/title_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="Favorite Genres"
            android:textColor="@color/grey1"
            android:textSize="@dimen/settings_settings_supernova_activity_title_text_view_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>