<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white"
    >

    <include
        layout="@layout/custom_action_bar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize" />

    <TextView
        android:id="@+id/you_are_atext_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="72dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="center"
        android:lineSpacingMultiplier="0.97"
        android:text="@string/sex_preference_activity_title"
        app:layout_constraintStart_toStartOf="@+id/button_preference_man_1"
        android:textColor="@color/grey1"
        android:textSize="@dimen/sex_preference_activity_you_are_atext_view_text_size"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <android.widget.Button
        android:id="@+id/button_preference_man_1"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="@dimen/sex_preference_activity_radio_button_unselected_button_width"
        android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
        android:background="@drawable/button_border_blue"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:lineSpacingMultiplier="1"
        android:text="@string/sex_preference_activity_men_button_text"
        android:textAllCaps="false"
        android:textColor="@color/color_primary"
        android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toStartOf="@+id/button_preference_woman_1"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/you_are_atext_view"/>

    <android.widget.Button
        android:id="@+id/button_preference_woman_1"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="@dimen/sex_preference_activity_radio_button_unselected_button_width"
        android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
        android:background="@drawable/button_border_blue"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:lineSpacingMultiplier="1"
        android:text="@string/sex_preference_activity_woman_button_text"
        android:textAllCaps="false"
        android:layout_marginTop="16dp"
        android:textColor="@color/color_primary"
        android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintTop_toBottomOf="@+id/you_are_atext_view"
        app:layout_constraintStart_toEndOf="@+id/button_preference_man_1"
        tools:text="@string/sex_preference_activity_interested_in_women_button_text" />

    <TextView
        android:id="@+id/looking_for_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="36dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="center"
        android:lineSpacingMultiplier="0.97"
        android:text="@string/sex_preference_activity_desc"
        app:layout_constraintStart_toStartOf="@+id/button_preference_man_1"
        android:textColor="@color/grey1"
        android:textSize="@dimen/sex_preference_activity_you_are_atext_view_text_size"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/button_preference_woman_1" />

    <android.widget.Button
        android:id="@+id/button_looking_for_man"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="@dimen/sex_preference_activity_radio_button_unselected_button_width"
        android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
        android:background="@drawable/button_border_blue"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:lineSpacingMultiplier="1"
        android:text="@string/sex_preference_activity_men_button_text"
        android:textAllCaps="false"
        android:textColor="@color/color_primary"
        android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toStartOf="@+id/button_preference_woman_1"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/looking_for_text_view"/>

    <android.widget.Button
        android:id="@+id/button_looking_for_woman"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="@dimen/sex_preference_activity_radio_button_unselected_button_width"
        android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
        android:background="@drawable/button_border_blue"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:lineSpacingMultiplier="1"
        android:text="@string/sex_preference_activity_woman_button_text"
        android:textAllCaps="false"
        android:layout_marginTop="16dp"
        android:textColor="@color/color_primary"
        android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintTop_toBottomOf="@+id/looking_for_text_view"
        app:layout_constraintStart_toEndOf="@+id/button_preference_man_1"
        tools:text="@string/sex_preference_activity_interested_in_women_button_text" />

    <android.widget.Button
        android:id="@+id/button_enable_location"
        style="?android:attr/borderlessButtonStyle"
        android:theme="@style/BottomCTAButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/sex_preference_activity_button_large_active_button_height"
        android:layout_marginStart="@dimen/intro1_activity_button_constraint_layout_margin_start"
        android:layout_marginTop="48dp"
        android:layout_marginEnd="@dimen/intro1_activity_button_constraint_layout_margin_end"
        android:background="@drawable/bottom_button_disabled_state"
        android:enabled="false"
        android:text="@string/next"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/button_looking_for_man" />

</androidx.constraintlayout.widget.ConstraintLayout>