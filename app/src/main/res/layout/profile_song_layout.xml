<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <TextView
            android:id="@+id/header_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:layout_marginStart="12dp"
            android:fontFamily="@font/inter_bold"
            android:gravity="left"
            android:lineSpacingMultiplier="1"
            android:text="Select a profile song. Let everyone know your music style."
            android:textColor="@color/grey1"
            android:textSize="20sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout" />

        <androidx.appcompat.widget.SearchView
            android:id="@+id/search_view"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="8dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            app:iconifiedByDefault="false"
            android:background="@color/color_tertiary_lighter"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header_title"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/songs_view_recycler_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/search_view"
            tools:listitem="@layout/settings_row" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header_normal_constraint_layout"
            android:layout_width="0dp"
            android:layout_height="@dimen/settings_settings_supernova_activity_header_normal_constraint_layout_height"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="0dp">

            <ImageButton
                android:id="@+id/account_header_left_image_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
                android:layout_marginTop="4dp"
                android:background="@null"
                android:elevation="2dp"
                android:padding="10dp"
                android:scaleType="center"
                android:src="@drawable/ic_icon_arrow_left"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/title_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_bold"
                android:gravity="center"
                android:lineSpacingMultiplier="1.09"
                android:text="Profile Song"
                android:textColor="@color/grey1"
                android:textSize="22sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/cancel_text_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/inter"
                android:gravity="end"
                android:lineSpacingMultiplier="1.1"
                android:padding="10dp"
                android:text="Cancel"
                android:visibility="gone"
                android:textColor="@color/grey2"
                android:textSize="@dimen/edit_profile1_activity_right_text_view_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
