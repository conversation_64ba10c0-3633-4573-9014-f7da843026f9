<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="75dp">

        <ImageButton
            android:id="@+id/account_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
            android:layout_marginTop="24dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left" />

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/messageListToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@color/white"
            android:minHeight="?attr/actionBarSize"
            android:orientation="horizontal">

        </androidx.appcompat.widget.Toolbar>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="false"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp">

            <ImageView
                android:id="@+id/user_avatar"
                android:layout_width="36dp"
                android:layout_height="36dp"
                tools:srcCompat="@tools:sample/avatars" />

            <TextView
                android:id="@+id/userName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_toEndOf="@+id/user_avatar"
                android:fontFamily="@font/inter_bold"
                android:lineSpacingExtra="2sp"
                android:text=""
                android:textColor="@color/grey1"
                android:textSize="16sp" />
        </RelativeLayout>
    </RelativeLayout>

    <com.stfalcon.chatkit.messages.MessagesList
        android:id="@+id/messagesList"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/input"
        android:layout_below="@+id/header"
        app:dateHeaderFormat="E, MMM dd"
        app:incomingAvatarHeight="56dp"
        app:incomingAvatarWidth="56dp"
        app:incomingTextColor="@color/grey1"
        app:incomingTextSize="16sp"
        app:incomingDefaultBubbleColor="@color/grey5"
        app:incomingDefaultBubbleSelectedColor="@color/grey5"
        app:incomingDefaultBubblePressedColor="@color/grey5"
        app:incomingTimeTextColor="@color/grey2"
        app:outcomingDefaultBubbleColor="@color/color_tertiary_lighter"
        app:outcomingDefaultBubbleSelectedColor="@color/color_tertiary_lighter"
        app:outcomingDefaultBubblePressedColor="@color/color_tertiary_lighter"
        app:outcomingTextColor="@color/grey1"
        app:outcomingTimeTextColor="@color/grey2"
        app:outcomingTextSize="16sp" />

    <RelativeLayout
        android:id="@+id/message_blank_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/input"
        android:layout_centerInParent="false"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="132dp"
        android:visibility="gone"
        android:layout_below="@+id/header">

        <ImageView
            android:id="@+id/message_blank_profile_image"
            android:layout_width="85dp"
            android:layout_height="85dp"
            android:layout_centerHorizontal="true" />

        <TextView
            android:id="@+id/message_blank_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:layout_marginTop="18dp"
            android:text="Make a Move"
            android:textSize="20sp"
            android:textColor="@color/grey1"
            android:fontFamily="@font/inter_bold"
            android:layout_below="@+id/message_blank_profile_image"
            android:layout_centerHorizontal="true"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:layout_marginTop="12dp"
            android:text="The quicker you act, the higher your chances are of a first-date!"
            android:textSize="16sp"
            android:textAlignment="center"
            android:textColor="@color/grey2"
            android:fontFamily="@font/inter"
            android:layout_below="@+id/message_blank_title"
            android:layout_centerHorizontal="true"/>
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/input"
        android:background="@color/grey4" />

    <com.stfalcon.chatkit.messages.MessageInput
        android:id="@+id/input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:fontFamily="@font/inter"
        app:inputButtonBackground="@drawable/ic_send_enabled"
        app:inputButtonDefaultIconColor="@color/white"
        app:inputButtonDefaultIconDisabledColor="@color/white60"
        app:inputButtonIcon="@drawable/ic_send_enabled"
        app:inputHint="Send Message..."
        app:showAttachmentButton="false" />

</RelativeLayout>