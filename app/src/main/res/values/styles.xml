<resources>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <!-- Edge-to-edge support for NoActionBar theme -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">false</item>
    </style>

    <style name="RowSettingsNotificationsTwoViewHolderSwitchOnSwitchTheme">
        <item name="colorControlActivated">@color/color_primary</item>
    </style>

    <style name="TabTextAppearance.Selected" parent="TextAppearance.Design.Tab">
        <item name="android:fontFamily">@font/inter_extrabold</item>
        <item name="fontFamily">@font/inter_extrabold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">20sp</item>
        <item name="textAllCaps">false</item>
    </style>
    <style name="TabTextAppearance.Unselected" parent="TextAppearance.Design.Tab">
        <item name="android:fontFamily">@font/inter_extrabold</item>
        <item name="fontFamily">@font/inter_extrabold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">20sp</item>
        <item name="textAllCaps">false</item>
    </style>

</resources>
