<resources>

	<!-- Project level resources -->
	<style name="AppTheme" parent="Theme.MaterialComponents.Light.DarkActionBar">
		<item name="colorPrimary">@color/color_primary</item>
		<item name="colorPrimaryDark">@color/color_primary</item>
		<item name="colorAccent">@color/color_secondary</item>
		<item name="android:windowContentOverlay">@null</item>
		<item name="android:windowDisablePreview">true</item>
		<item name="android:windowIsTranslucent">true</item>
		<!-- Removed deprecated android:statusBarColor for Android 15 edge-to-edge compatibility -->
		<!-- <item name="android:statusBarColor">@color/white</item> -->
		<!-- Removed android:fitsSystemWindows to allow edge-to-edge content -->
		<!-- <item name="android:fitsSystemWindows">true</item> -->
		<item name="android:windowLightStatusBar">true</item>
		<item name="android:windowDrawsSystemBarBackgrounds">true</item>
		<!-- Enable edge-to-edge display -->
		<item name="android:statusBarColor">@android:color/transparent</item>
		<item name="android:navigationBarColor">@android:color/transparent</item>
		<item name="android:enforceStatusBarContrast">false</item>
		<item name="android:enforceNavigationBarContrast">false</item>
	</style>

    <style name="BottomCTAButton">
		<item name="android:fontFamily">@font/inter_bold</item>
		<item name="android:textSize">18sp</item>
		<item name="android:lineSpacingMultiplier">1</item>
		<item name="android:gravity">center</item>
		<item name="android:textAllCaps">false</item>
	</style>

</resources>