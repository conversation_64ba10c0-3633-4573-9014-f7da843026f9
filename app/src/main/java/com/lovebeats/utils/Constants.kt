package com.lovebeats.utils

object Constants {

    // user info keys
    const val phoneNumber = "phone"
    const val email = "email"
    const val latitude = "latitude"
    const val longitude = "longitude"
    const val city = "city"
    const val country = "country"
    const val state = "state"
    const val zip = "zip"
    const val name = "name"
    const val dob = "dob"
    const val gender = "gender"
    const val genderOrientation = "genderOrientation"
    const val height = "height"
    const val profession = "profession"
    const val school = "school"
    const val school2 = "school2"
    const val homeTown = "homeTown"
    const val iceBreaker1 = "iceBreaker1"
    const val iceBreaker2 = "iceBreaker2"
    const val isOnboardingComplete = "isOnboardingComplete"
    const val iceBreakersComplete = "iceBreakersComplete"
    const val createdOn = "createdOn"
    const val modifiedOn = "modifiedOn"
    const val externalId = "externalId"

    const val users = "users"
    const val fireStoreUsers = "users"
    const val geofire = "geofire"
    const val cities = "cities"
    const val likeCounter = "likeCounter"
    const val lCount = "lCount"
    const val lDate = "lDate"

    const val apiKeys = "apiKeys"
    const val places = "places"

    // city info
    const val lat = "lat"
    const val lng = "lng"
    const val noLimit = "No Limit"

    // subscription status
    const val lovebeatPlus = "plus"
    const val isActive = "isActive"

    const val connections = "connections"
    const val dislikedBy = "dislikedBy"
    const val likedBy = "likedBy"
    const val likes = "likes"
    const val dislikes = "dislikes"
    const val matches = "matches"
    const val unmatchedUsers = "unmatchedUsers"

    const val lSeen = "lSeen"
    const val lScreen = "lscreen"

    const val firebaseUserId = "firebaseUserId"
    const val userProfilePhotoUrl = "userProfilePhoto"

    const val loveBeatAccountPrefs = "love_beat_account_prefs"

    //spaces
    const val spacesConfigs = "spacesConfigs"

    //settings
    const val showNewMatches = "showNewMatches"
    const val showMessages = "showMessages"
    const val minMatchHeight = "minMatchHeight"
    const val maxMatchHeight = "maxMatchHeight"
    const val minMatchAge = "minMatchAge"
    const val maxMatchAge = "maxMatchAge"
    const val interestedIn = "interestedIn"
    const val distance = "distance"

    const val likesSeenMap = "likesSeenMap"

    const val tutorialShown = "tutorialShown"
    const val reviewShown = "reviewShown"
    const val freeTrailShown = "freeTrailShown"

    const val reportedUsers = "reported_users"
    const val reportedBy = "reportedBy"

    // favorites
    const val favorites = "favorites"
    const val movieGenres = "movieGenres"
    const val musicGenres = "musicGenres"
    const val profileSong = "profileSong"
    const val requestedFavorites = "requestedFavorites"
    const val songId = "id"
    const val songTitle = "title"
    const val songSubTitle = "subTitle"
    const val songUrl = "audioUrl"
    const val songImageUrl = "imageUrl"
    const val spotifyUrl = "spotifyUrl"


    //chat related
    const val conversations = "conversations"
    const val createdBy = "createdBy"
    const val lastMessage = "message"
    const val timestamp = "timestamp"
    const val sendTo = "sentTo"
    const val unread = "unread"
    const val avatar = "avatar"
    const val user0 = "user0"
    const val user1 = "user1"
    const val conversationId = "conversationId"
    const val chatId = "chatId"
    const val message = "message"

    const val defaultDistance = 100

    const val isNewMessageReceived = "isNewMessageReceived"

    const val android = "android"

    const val deviceToken = "deviceToken"
    const val deviceOs = "deviceOs"
    const val deviceHardwareIdentifier = "deviceHardwareIdentifier"
    const val deviceDetails = "deviceDetails"
    const val version = "version"

    //1 foot = 30.48 cms
    const val cmMultiplier = 30.48

    //1 inch = 2.54 cms
    const val inchMultiplier = 2.54

    //age multiplier
    const val startingAge = 18
    const val sliderMaxAge = 80

    // (maxage - startingage)/100
    const val ageMutiplier = 0.62

    // Height attributes
    //1 foot = 12 inches
    //starting height is the min height set which is 4'0"
    const val startingHeightInInch: Int = 48 // 4'0"
    const val defaultHeightInInch = 66.0
    const val defaultHeightInFeet: String = "5ft6in"

    //here min height is 4'0" which is 48 and max height is 7'0" which 84 so (84 - 48) = 36/100 = 0.36
    const val heightInInchMultiplier = 0.36
    const val preferencesSliderStartingHeight: Int = 48 // 4'0"
    const val preferencesSliderMaxHeight = 84.0 //7'0"

    //Interested in default range heights
    const val defaultMinHeightInInch: Double = 48.0 //4'0"
    const val defaultMaxHeightInInch: Double = 84.0 //7'0"

    //Fragment Arguments
    const val PREFERENCES_FRAGMENT_ARG1 = "preferences_fragment_arg1"
    const val PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY = "homeprofiletabactivity"
    const val PREFERENCES_FRAGMENT_SETTINGS_SETTINGS_FRAGMENT = "settingssettingsfragment"
    const val PREFERENCES_FRAGMENT_TELEPORT_FRAGMENT = "hometeleportfragment"

    //time
    const val systemTimeSaved = "systemTimeSaved"

    // lovebeats email
    const val lovebeatAdminEmail = "<EMAIL>"

    // influencer promotions
    const val influencerSignups = "influencerSignups"
    const val influencerId = "influencerId"
    const val influencerName = "influencerName"
    const val freeTrailUsed = "freeTrailUsed"
    const val promoCounter = "promoCounter"
    const val influencerPromoShown = "influencerPromoShown"
    const val installSource = "installSource"
    const val branchCustomMetaKey = "$" + "custom_meta_tags"

    // Branch
    const val source = "source"

    // Photo file names
    const val photoFileName1 = "1.webp"
    const val photoFileName2 = "2.webp"
    const val photoFileName3 = "3.webp"
    const val photoFileName4 = "4.webp"
    const val photoFileName5 = "5.webp"
    const val photoFileName6 = "6.webp"

    const val dmRequest = "dmRequest"
    const val dmRequestBy = "dmRequestBy"
    const val dmRequestRejected = "dmRequestRejected"
    // Pagination constants
    const val DEFAULT_PAGE_SIZE = 20
    const val PASSED_USERS_PAGE_SIZE = 50
    const val INITIAL_PAGE = 1
    const val VISIBLE_THRESHOLD = 5  // Start loading more items when user is 5 items away from the end
}