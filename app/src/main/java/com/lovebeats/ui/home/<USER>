package com.lovebeats.ui.home

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import com.lovebeats.R
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.glide.ImageLoaderModule
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.SpacesImagesViewModel
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Constants.conversations
import com.google.firebase.database.FirebaseDatabase
import kotlin.let
import kotlin.text.isNotEmpty
import kotlin.text.trim

class SendMessageDialogFragment(val spacesImagesViewModel: SpacesImagesViewModel, val onMessageSent: () -> Unit) : DialogFragment() {

    private var profilePicUrl: String? = null
    private var name: String? = null
    private var uid: String? = null

    companion object {
        fun newInstance(uid: String, profilePicUrl: String, name: String, spacesImagesViewModel: SpacesImagesViewModel, onMessageSent: () -> Unit): SendMessageDialogFragment {
            val fragment = SendMessageDialogFragment(spacesImagesViewModel = spacesImagesViewModel, onMessageSent)
            val args = Bundle()
            args.putString("profilePicUrl", profilePicUrl)
            args.putString("name", name)
            args.putString("uid", uid)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.dialog_send_dm, container, false)

        val closeButton: ImageView = view.findViewById(R.id.close_button)
        val userProfileImage: ImageView = view.findViewById(R.id.userProfileImage)
        val titleTextView: TextView = view.findViewById(R.id.title)
        val sendButton: Button = view.findViewById(R.id.sendButton)
        val messageInput: EditText = view.findViewById(R.id.messageInput)

        // Retrieve arguments
        arguments?.let {
            profilePicUrl = it.getString("profilePicUrl")
            name = it.getString("name")
            uid = it.getString("uid")
        }

        titleTextView.text = "Send $name a Message"

        activity?.let { activity->
            profilePicUrl?.let { picId ->
                ImageLoaderModule.loadImageIntoImageViewWithSpacesPathCircleTransformAndLoading(activity, spacesImagesViewModel, picId, userProfileImage)
            }
        }

        closeButton.setOnClickListener {
            dismiss()
        }

        sendButton.setOnClickListener {
            val message = messageInput.text.toString().trim()
            if (message.isNotEmpty()) {
                sendDirectMessage(activity, uid!!, message)
                onMessageSent()
                dismiss()
            }
        }

        val charCount: TextView = view.findViewById(R.id.charCount)

        messageInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                charCount.text = "${s?.length ?: 0}/180"
            }

            override fun afterTextChanged(s: Editable?) {}
        })

        return view
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    private fun sendDirectMessage(
        context: Context?,
        otherUserId: String,
        message: String
    ) {
        context?.let {
            val mainUserFirebaseId = AccountPreferences.getInstance(context).getStringValue(Constants.firebaseUserId, "")
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
            firebaseDatabaseUtil.matchUsers(mainUserFirebaseId, otherUserId, null, true, message, mainUserFirebaseId)
        }
    }
}
