package com.lovebeats.ui.home.settings

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import com.lovebeats.R
import com.lovebeats.databinding.ActivityUpdateEmailBinding
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils
import com.lovebeats.utils.Utils.Companion.afterTextChanged
class UpdateEmailActivity : BaseEdgeToEdgeActivity() {

    private lateinit var binding: ActivityUpdateEmailBinding

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, UpdateEmailActivity::class.java)
        }
    }

    private lateinit var buttonLargeActiveButton: Button
    private lateinit var thisEnsuresThatYoTextView: TextView
    private lateinit var emailEditText: EditText

    private var email: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUpdateEmailBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Apply edge-to-edge window insets
        applyWindowInsets()
        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }


        emailEditText = this.findViewById(R.id.email_edit_text)

        val email = AccountPreferences.getInstance(this).getStringValue(Constants.email, "")

        emailEditText.setText(email.toString())

        emailEditText.afterTextChanged {
            if (it.isNotEmpty()) {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                buttonLargeActiveButton.isEnabled = true
            } else {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                buttonLargeActiveButton.isEnabled = false
            }
        }

        binding.saveTextView.setOnClickListener {
            saveTextViewClicked()
        }

        binding.cancelTextView.setOnClickListener {
            cancelTextViewClicked()
        }
    }

    fun onButtonLargeActivePressed() {

        email = emailEditText.text.toString()

        val isValidEmail = Utils.isValidEmail(email)

        if (isValidEmail) {

            val accountPreferences = AccountPreferences.getInstance(applicationContext)
            accountPreferences.setValue(Constants.email, email)

            saveEmailToFirebase()

        } else {
            emailEditText.error = "Please enter a valid email address."
        }
    }

    private fun saveEmailToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.email, email)

        val intent = Intent(this, SettingsActivity::class.java)
        intent.putExtra("AccountSettingsUpdateEmailOrPhoneActivity", true)
        startActivity(intent)
    }

    private fun saveTextViewClicked() {
        onButtonLargeActivePressed()
    }

    private fun cancelTextViewClicked() {
        val intent = Intent(this, SettingsActivity::class.java)
        intent.putExtra("AccountSettingsUpdateEmailOrPhoneActivity", true)
        startActivity(intent)
    }
}
