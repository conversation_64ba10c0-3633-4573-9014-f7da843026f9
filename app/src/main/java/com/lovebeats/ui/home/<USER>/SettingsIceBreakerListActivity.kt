package com.lovebeats.ui.home.settings

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.R
import com.lovebeats.ui.adapter.Icebreaker1ActivityTableRecyclerViewAdapter
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils

class SettingsIceBreakerListActivity : BaseEdgeToEdgeActivity() {

    private lateinit var chooseTwoProfileQtextView: TextView
    private lateinit var tableRecyclerView: RecyclerView
    private lateinit var cancelTextView: TextView
    private lateinit var headerLeftImageView: ImageView

    private val questions: ArrayList<String> = ArrayList()
    private var icebreaker: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings_ice_breaker_list)
        
        // Apply edge-to-edge window insets
        applyWindowInsets()

        icebreaker = intent.getStringExtra("icebreaker") ?: ""

        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun addQuestions() {

        val questionsMap = Utils.getIceBreakerQuestions()

        for (key in questionsMap.keys) {
            questions.add(key)
        }
    }

    private fun init() {

        // Configure table component
        tableRecyclerView = this.findViewById(R.id.table_recycler_view)
        tableRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        cancelTextView = this.findViewById(R.id.cancel_text_view)

        headerLeftImageView = this.findViewById(R.id.edit_profile_header_left_image_view)

        addQuestions()

        val questionsAdapter = Icebreaker1ActivityTableRecyclerViewAdapter(questions, this)
        tableRecyclerView.adapter = questionsAdapter

        questionsAdapter.setOnItemClickListener(object : Icebreaker1ActivityTableRecyclerViewAdapter.OnItemClickListener {
            override fun onClick(view: View, data: String) {
                startIceBreakerActivity(data)
            }
        })

        cancelTextView.setOnClickListener {
            onCancelButtonPressed()
        }

        headerLeftImageView.setOnClickListener {
            startSettingsIceBreakerTitleActivity()
        }
    }

    private fun onCancelButtonPressed() {
        this.startActivity(EditProfileModalActivity.newIntent(this))
    }

    private fun startSettingsIceBreakerTitleActivity() {

        val intent = Intent(this, SettingsIceBreakerTitleActivity::class.java)
        startActivity(intent)
    }

    private fun startIceBreakerActivity(data: String) {

        val intent = Intent(this, SettingsIceBreakerTitleActivity::class.java)
        intent.putExtra("question", data)
        intent.putExtra("icebreaker", icebreaker)
        startActivity(intent)
    }
}
