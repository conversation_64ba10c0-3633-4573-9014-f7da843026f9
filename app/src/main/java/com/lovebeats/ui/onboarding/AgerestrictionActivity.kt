package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import com.lovebeats.databinding.AgeRestrictionActivityBinding
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.ScreenRouter.Companion.setLastSeenScreen
import com.lovebeats.utils.Utils.Companion.openGmailApp
class AgerestrictionActivity : BaseEdgeToEdgeActivity() {

    private lateinit var binding: AgeRestrictionActivityBinding


    companion object {

        const val TAG = ScreenRouter.AGE_RESTRICTION_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, AgerestrictionActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = AgeRestrictionActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Apply edge-to-edge window insets
        applyWindowInsets()

        binding.buttonLargeActiveButton.setOnClickListener {
            openGmailApp(this, "")
        }

        setLastSeenScreen(TAG, this)

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }
}
