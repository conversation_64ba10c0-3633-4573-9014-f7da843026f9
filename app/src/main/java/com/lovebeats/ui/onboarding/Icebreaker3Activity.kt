package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.R
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.ui.adapter.Icebreaker3ActivityTableRecyclerViewAdapter
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import com.lovebeats.ui.home.browseProfiles.BrowseProfilesActivity
import com.lovebeats.ui.onboarding.Icebreaker1Activity.Companion.ICE_BREAKER_1_ANSWER
import com.lovebeats.ui.onboarding.Icebreaker1Activity.Companion.ICE_BREAKER_1_QUESTION
import com.lovebeats.ui.onboarding.Icebreaker1Activity.Companion.ICE_BREAKER_2_QUESTION
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils


class Icebreaker3Activity : BaseEdgeToEdgeActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, Icebreaker3Activity::class.java)
        }
    }

    private lateinit var selectOneMoreQuesTextView: TextView
    private lateinit var tableRecyclerView: RecyclerView
    private lateinit var skipTextView: TextView

    private var icebreaker1Question: String? = ""
    private var icebreaker1Answer: String? = ""

    private val questions: ArrayList<String> = ArrayList()

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.icebreaker3_activity)

        // Apply edge-to-edge window insets
        applyWindowInsets()
        this.init()
    }

    private fun init() {

        icebreaker1Question = intent?.getStringExtra(ICE_BREAKER_1_QUESTION)
        icebreaker1Answer = intent?.getStringExtra(ICE_BREAKER_1_ANSWER)

        // Configure Select one more ques component
        selectOneMoreQuesTextView = this.findViewById(R.id.select_one_more_ques_text_view)
        val selectOneMoreQuesTextViewText = SpannableString(this.getString(R.string.icebreaker3_activity_select_one_more_ques_text_view_text))
        selectOneMoreQuesTextView.text = selectOneMoreQuesTextViewText

        // Configure table component
        tableRecyclerView = this.findViewById(R.id.table_recycler_view)
        tableRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        addQuestions()

        val questionsAdapter = Icebreaker3ActivityTableRecyclerViewAdapter(questions, this)

        tableRecyclerView.adapter = questionsAdapter

        questionsAdapter.setOnItemClickListener(object : Icebreaker3ActivityTableRecyclerViewAdapter.OnItemClickListener {
            override fun onClick(view: View, data: String) {
                startIceBreaker4Activity(data)
            }
        })

        setupToolbar()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    fun setupToolbar() {

        skipTextView = this.findViewById(R.id.skip_text_view)

        skipTextView.setOnClickListener {

            Icebreaker1Activity.abandonIceBreakers(this)
        }
    }

    private fun onSkipButtonPressed() {
        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    private fun startIceBreaker4Activity(question: String) {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {

            val intent = Intent(this, Icebreaker4Activity::class.java)
            intent.putExtra(ICE_BREAKER_1_QUESTION, icebreaker1Question)
            intent.putExtra(ICE_BREAKER_1_ANSWER, icebreaker1Answer)
            intent.putExtra(ICE_BREAKER_2_QUESTION, question)

            startActivity(intent)
        }
    }

    private fun addQuestions() {

        val questionsMap = Utils.getIceBreakerQuestions()

        for (key in questionsMap.keys) {
            questions.add(key)
        }

        if (!TextUtils.isEmpty(icebreaker1Question)) {

            questions.remove(icebreaker1Question)
        }
    }
}
