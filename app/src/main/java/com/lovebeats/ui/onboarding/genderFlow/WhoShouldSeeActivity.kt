package com.lovebeats.ui.onboarding.genderFlow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import com.lovebeats.R
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.analytics.USER_GENDER_PROPERTY
import com.lovebeats.databinding.ActivityWhoShouldSeeBinding
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.GenderType
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter

class WhoShouldSeeActivity : BaseEdgeToEdgeActivity() {

    private lateinit var binding: ActivityWhoShouldSeeBinding

    companion object {

        const val TAG = ScreenRouter.WHO_SHOULD_SEE_ACTIVITY

        fun newIntent(context: Context): Intent {
            return Intent(context, WhoShouldSeeActivity::class.java)
        }
    }

    private var gender: String = ""
    private var genderButtonSelected = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWhoShouldSeeBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Apply edge-to-edge window insets
        applyWindowInsets()

        ScreenRouter.setLastSeenScreen(TAG, this)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        binding.notificationsHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.buttonPreferenceMen.setOnClickListener {
            this.onMenButtonPressed()
        }

        binding.buttonPreferenceWomen.setOnClickListener { view ->
            this.onWomenButtonPressed()
        }

        binding.buttonNext.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }
    }

    private fun onMenButtonPressed() {
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = GenderType.man.toString()
        genderButtonSelected = true
        enableOrDisableButton()
    }

    private fun onWomenButtonPressed() {
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = GenderType.woman.toString()
        genderButtonSelected = true
        enableOrDisableButton()
    }

    private fun enableOrDisableButton() {
        if (genderButtonSelected) {
            binding.buttonNext.isEnabled = true
            binding.buttonNext.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        }
    }

    fun onButtonLargeActivePressed() {
        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            saveGenderPrefs()
            startInterestedInActivity()
        }
    }

    private fun saveGenderPrefs() {
        AccountPreferences.getInstance(this).setValue(Constants.gender, gender)

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.gender, gender)

        AnalyticsTrackingService.setUserProperty(this, USER_GENDER_PROPERTY, gender)
        MixPanelAnalyticsTrackingService.setUserProperty(this, USER_GENDER_PROPERTY, gender)
    }

    private fun startInterestedInActivity() {
        this.startActivity(InterestedInActivity.newIntent(this))
    }
}