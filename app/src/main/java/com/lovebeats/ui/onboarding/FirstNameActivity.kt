package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.TextUtils
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import com.lovebeats.R
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.onboarding.genderFlow.IdentifyYourselfActivity
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.ScreenRouter.Companion.setLastSeenScreen
import com.lovebeats.utils.Utils.Companion.afterTextChanged

class FirstNameActivity : BaseEdgeToEdgeActivity() {

    companion object {

        const val TAG = ScreenRouter.FIRST_NAME_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, FirstNameActivity::class.java)
        }
    }

    private lateinit var buttonLargeActiveButton: Button
    private lateinit var thisWillBeDisplayTextView: TextView

    private lateinit var firstNameEditText: EditText

    private var firstName: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.first_name_activity)
        
        // Apply edge-to-edge window insets
        applyWindowInsets()
        this.init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        // Configure This will be display component
        thisWillBeDisplayTextView = this.findViewById(R.id.this_will_be_display_text_view)
        val thisWillBeDisplayTextViewText = SpannableString(this.getString(R.string.first_name_screen_desc))
        thisWillBeDisplayTextView.text = thisWillBeDisplayTextViewText

        firstNameEditText = this.findViewById(R.id.first_name_edit_text)
        firstNameEditText.requestFocus()

        firstNameEditText.afterTextChanged {
            if (it.isNotEmpty()) {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                buttonLargeActiveButton.isEnabled = true
            } else {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                buttonLargeActiveButton.isEnabled = false
            }
        }
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            firstName = firstNameEditText.text.toString()

            if (!TextUtils.isEmpty(firstName)) {

                val accountPreferences = AccountPreferences.getInstance(this)
                accountPreferences.setValue(Constants.name, firstName)

                saveFirstNameToFirebase()

                val email = AccountPreferences.getInstance(this).getStringValue(Constants.email, "")
                if (email.isNullOrEmpty()) {
                    startEmailActivity()
                } else {
                    startSexPreferenceActivity()
                }
            }
        }
    }

    private fun saveFirstNameToFirebase() {

        AccountPreferences.getInstance(this).setValue(Constants.name, firstName)

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.name, firstName)
    }

    private fun startEmailActivity() {
        this.startActivity(EmailActivity.newIntent(this))
    }

    private fun startSexPreferenceActivity() {
        this.startActivity(IdentifyYourselfActivity.newIntent(this))
    }
}
