package com.lovebeats.ui.login

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.view.MenuItem
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import com.lovebeats.R
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils.Companion.validate

class PhoneNumberActivity : BaseEdgeToEdgeActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, PhoneNumberActivity::class.java)
        }
    }

    private lateinit var weLlTextYouAcodTextView: TextView
    private lateinit var textViewTextView: TextView
    private lateinit var buttonLargeActiveButton: Button
    private lateinit var phoneNumberEditText: EditText

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.phone_number_activity)
        
        // Apply edge-to-edge window insets
        applyWindowInsets()
        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure We'll text you a cod component
        weLlTextYouAcodTextView = this.findViewById(R.id.we_ll_text_you_acod_text_view)
        val weLlTextYouAcodTextViewText = SpannableString(this.getString(R.string.ph_no_desc))
        weLlTextYouAcodTextView.text = weLlTextYouAcodTextViewText

        // Configure +91 component
        textViewTextView = this.findViewById(R.id.height_text_view)
        val textViewTextViewText = SpannableString(this.getString(R.string.ph_no_country_code))
        textViewTextView.text = textViewTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.isEnabled = false

        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        phoneNumberEditText = this.findViewById(R.id.phone_number_edit_text)
        phoneNumberEditText.requestFocus()

        phoneNumberEditText.validate("Please enter a valid 10 digit phone number.") { s ->
            isValidPhone(s)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            val phoneNumber = phoneNumberEditText.text

            val removeSpecialChars = Regex("[^\\d]")
            val unMaskedPhone = removeSpecialChars.replace(phoneNumber, "")

            if (unMaskedPhone.length == 10) {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
            }

            val accountPreferences = AccountPreferences.getInstance(applicationContext)
            accountPreferences.setValue(Constants.phoneNumber, unMaskedPhone)
            startCodeVerificationActivity()
        }
    }

    private fun startCodeVerificationActivity() {

        this.startActivity(CodeVerificationActivity.newIntent(this))
    }

    private fun isValidPhone(input: String): Boolean {
        val removeSpecialChars = Regex("[^\\d]")
        val unMaskedPhone = removeSpecialChars.replace(input, "")

        return if (unMaskedPhone.length == 10) {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
            buttonLargeActiveButton.isEnabled = true
            true
        } else {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
            buttonLargeActiveButton.isEnabled = false
            false
        }
    }
}
