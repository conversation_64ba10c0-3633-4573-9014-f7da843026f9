package com.lovebeats.ui.favorites

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.lovebeats.R
import com.lovebeats.databinding.ActivityFavoritesSettingsBinding
import com.lovebeats.models.FavoritesType
import com.lovebeats.ui.adapter.SettingsHomeSupernovaActivityViewRecyclerViewAdapter
import com.lovebeats.ui.favorites.FavoritesActivity.Companion.EXTRA_FAVORITE
import com.lovebeats.utils.ScreenRouter
class FavoritesSettingsActivity : BaseEdgeToEdgeActivity() {

    private lateinit var binding: ActivityFavoritesSettingsBinding

    private val favoritesData: ArrayList<String> = ArrayList()

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, FavoritesSettingsActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFavoritesSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Apply edge-to-edge window insets
        applyWindowInsets()

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, FavoritesSettingsActivity::class.java.simpleName)
    }

    private fun init() {

        if (favoritesData.isEmpty()) {
            addFavoriteSettingsData()
        }

        // Configure View component
        binding.favoritesViewRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        binding.favoritesViewRecyclerView.adapter = SettingsHomeSupernovaActivityViewRecyclerViewAdapter(this, favoritesData, { settingsItem: String -> favoritesItemClicked(settingsItem) }, 1)

        binding.accountHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }
    }

    private fun addFavoriteSettingsData() {
        favoritesData.add(resources.getString(R.string.favorites_item_1))
        favoritesData.add(resources.getString(R.string.favorites_item_2))
        favoritesData.add(resources.getString(R.string.favorites_item_3))
        favoritesData.add(resources.getString(R.string.favorites_item_4))
    }

    private fun favoritesItemClicked(settingsItem: String) {
        val intent = Intent(this, FavoritesActivity::class.java)
        when (settingsItem) {
            resources.getString(R.string.favorites_item_1) -> {
                intent.putExtra(EXTRA_FAVORITE, FavoritesType.actor)
            }
            resources.getString(R.string.favorites_item_2) -> {
                intent.putExtra(EXTRA_FAVORITE, FavoritesType.actress)
            }
            resources.getString(R.string.favorites_item_3) -> {
                intent.putExtra(EXTRA_FAVORITE, FavoritesType.musician)
            }
            resources.getString(R.string.favorites_item_4) -> {
                intent.putExtra(EXTRA_FAVORITE, FavoritesType.singer)
            }
        }
        startActivity(intent)
    }
}