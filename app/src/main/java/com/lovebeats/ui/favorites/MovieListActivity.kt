package com.lovebeats.ui.favorites

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.lovebeats.R
import com.lovebeats.databinding.ActivityGenresBinding
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.favorites.MusicListActivity.Companion.EXTRA_FROM_SETTINGS
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils.Companion.getMovieGenres

class MovieListActivity : BaseEdgeToEdgeActivity() {

    private lateinit var binding: ActivityGenresBinding
    private lateinit var genresListRecyclerAdapter: GenresListRecyclerAdapter
    private var selectedGenres = mutableSetOf<String>()

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, MovieListActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGenresBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Apply edge-to-edge window insets
        applyWindowInsets()
        init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {
        binding.accountHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        val layoutManager = FlexboxLayoutManager(this)
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.justifyContent = JustifyContent.FLEX_START

        val fromSettings = intent.getBooleanExtra(EXTRA_FROM_SETTINGS, false)
        if (fromSettings) {
            selectedGenres = AccountPreferences.getInstance(this).getStringSetValue(Constants.movieGenres, mutableSetOf())
            if (selectedGenres.size >= 3) {
                binding.buttonNext.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                binding.buttonNext.isEnabled = true
            }
        }

        binding.favoritesViewRecyclerView.layoutManager = layoutManager
        genresListRecyclerAdapter = GenresListRecyclerAdapter(getMovieGenres(), this, selectedGenres)
        binding.favoritesViewRecyclerView.adapter = genresListRecyclerAdapter
        genresListRecyclerAdapter.setOnItemClickListener(object : GenresListRecyclerAdapter.OnItemClickListener {
            override fun onClick(view: View, data: Set<String>) {
                selectedGenres = mutableSetOf()
                selectedGenres.addAll(data)
            }

            override fun enableDisableButton(enable: Boolean) {
                if (enable) {
                    binding.buttonNext.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                    binding.buttonNext.isEnabled = true
                } else {
                    binding.buttonNext.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                    binding.buttonNext.isEnabled = false
                }
            }
        })

        binding.buttonNext.setOnClickListener {
            if (binding.buttonNext.isEnabled) {
                updateFavoriteToFirebase(selectedGenres)
                startMusicGenresActivity()
            }
        }
    }

    private fun updateFavoriteToFirebase(favorite: Set<String>) {
        AccountPreferences.getInstance(this).setValue(Constants.movieGenres, favorite)
        val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
        firebaseDatabaseUtil.saveUserInfoToFirebase(Constants.movieGenres, favorite)
    }

    private fun startMusicGenresActivity() {
        val fromSettings = intent.getBooleanExtra(EXTRA_FROM_SETTINGS, false)
        val intent = Intent(this, MusicListActivity::class.java)
        if (fromSettings) {
            intent.putExtra(EXTRA_FROM_SETTINGS, true)
        }
        startActivity(intent)
    }
}
