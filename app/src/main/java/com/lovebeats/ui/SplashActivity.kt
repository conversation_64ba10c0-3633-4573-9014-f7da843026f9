package com.lovebeats.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.firestore.DocumentSnapshot
import com.lovebeats.R
import com.lovebeats.analytics.MP_SCREEN_NAME
import com.lovebeats.analytics.MP_SCREEN_VIEWED
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.deepLinking.AppInvites
import com.lovebeats.deepLinking.PromotionsHandler.handleBranchCampaignSignups
import com.lovebeats.analytics.MetaAdsHandler
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.firebase.config.RemoteConfigurationService
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.firebase.database.FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore
import com.lovebeats.models.User
import com.lovebeats.services.AuthService
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.home.browseProfiles.BrowseProfilesActivity
import com.lovebeats.ui.login.Intro1Activity
import com.lovebeats.ui.login.Intro1Activity.Companion.IS_USER_REPORTED
import com.lovebeats.ui.login.Intro1Activity.Companion.REPORTED_USER_MESSAGE
import com.lovebeats.ui.login.Intro1Activity.Companion.REPORTED_USER_TITLE
import com.lovebeats.ui.login.Intro1Activity.Companion.SHOULD_DELETE_USER
import com.lovebeats.utils.AppUpdateHelperUtility
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Constants.branchCustomMetaKey
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils.Companion.isValidUser
import com.lovebeats.ui.base.BaseEdgeToEdgeActivity
import io.branch.referral.Branch
import org.json.JSONObject
import timber.log.Timber
import java.util.*
import kotlin.concurrent.timerTask

@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseEdgeToEdgeActivity() {

    private val splashDisplayLength: Long = 500
    private val mContext: Context = this
    private lateinit var mActivity: Activity
    private lateinit var appUpdateHelperUtility: AppUpdateHelperUtility

    private lateinit var title: TextView

    override fun onStart() {
        super.onStart()

        initBranch()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)
        mActivity = this

        title = this.findViewById(R.id.title)

        val spannableString: Spannable = SpannableString(getString(R.string.app_name))
        spannableString.setSpan(ForegroundColorSpan(getColor(R.color.color_primary)), 0, 4, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        title.text = spannableString

        appUpdateHelperUtility = AppUpdateHelperUtility(this)

        RemoteConfigurationService.fetchConfig(RemoteConfigurationService.APP_FORCE_UPDATE_VERSION) { upgradeVersion ->
            if (!upgradeVersion.isNullOrEmpty() &&
                appUpdateHelperUtility.isImmediateUpdateRequired(upgradeVersion, application)) {

                appUpdateHelperUtility.checkIfAppUpdateAvailable { isAppUpdateAvailable ->

                    if (isAppUpdateAvailable) {

                        appUpdateHelperUtility.startImmediateUpdate()
                    } else {

                        showNextSteps()
                    }
                }
            } else {

                showNextSteps()
            }
        }

        val map = hashMapOf<String, String>()
        map[MP_SCREEN_NAME] = this::class.java.simpleName
        MixPanelAnalyticsTrackingService.logEvent(this, MP_SCREEN_VIEWED, map)

        // Handle Meta Ads attribution from app links
        handleMetaAdsAttribution()
    }

    override fun onResume() {
        super.onResume()
        // check If an in-app update is already in progress, if yes then resume the update.
        appUpdateHelperUtility.startImmediateUpdateIfInProgress()
    }

    private fun showNextSteps() {
        val signUpStatus = AccountPreferences.getInstance(this).getBooleanValue(Constants.isOnboardingComplete, false)
        val splashTimer = Timer("SplashTimer")

        when {
            signUpStatus -> {
                splashTimer.schedule(timerTask {
                    checkOnboardingStatus()
                }, splashDisplayLength)
            }
            else -> {
                splashTimer.schedule(timerTask {

                    handleInitialRoute()
                }, splashDisplayLength)
            }
        }

        AppInvites.handleDynamicLinks(intent, this)
    }

    private fun checkOnboardingStatus() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        }

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.readMainUserMapRawDataFromFirebaseFirestore(object :
            FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore {
            override fun onSuccess(userDocumentSnapShot: DocumentSnapshot) {

                if (userDocumentSnapShot.contains(Constants.isOnboardingComplete)) {

                    val user = userDocumentSnapShot.toObject(User::class.java)

                    val isOnboardinComplete: Boolean? = user?.isOnboardingComplete
                    val isUserReported: Boolean? = user?.isUserReported
                    val shouldDelete = user?.rsDel?: false

                    if (isUserReported == true) {

                        val reportedUserTitle: String? = user.rTitle as? String
                        val reportedUserMessage: String? = user.rMsg as? String

                        val intent = Intro1Activity.newIntent(this@SplashActivity).launchModeWithNoBackStack()
                        intent.putExtra(IS_USER_REPORTED, true)
                        intent.putExtra(SHOULD_DELETE_USER, shouldDelete)
                        intent.putExtra(REPORTED_USER_TITLE, reportedUserTitle)
                        intent.putExtra(REPORTED_USER_MESSAGE, reportedUserMessage)
                        startActivity(intent)
                    } else if (isOnboardinComplete == true && isValidUser(user)) {

                        showHomeActivity()

                    } else {

                        showIntroActivity()
                    }
                } else {

                    handleInitialRoute()
                }
            }

            override fun onFailure() {

                showIntroActivity()
            }
        })
    }

    fun showHomeActivity() {

        val intent = Intent(mContext, BrowseProfilesActivity::class.java).launchModeWithNoBackStack()
        mContext.startActivity(intent)

        FirebaseDatabaseUtil(this).saveLastSeen()
    }

    fun handleInitialRoute() {
        val lastSeenScreen = ScreenRouter.getLastScreenScreen(mContext)
        if ("" != lastSeenScreen) {
            mActivity.runOnUiThread {
                showAlertDialogForLastSeenScreen(lastSeenScreen)
            }
        } else {
            showIntroActivity()
        }
    }

    private fun showIntroActivity() {
        val intent = Intent(this, Intro1Activity::class.java)
        finish()
        this.startActivity(intent)
    }

    private fun showAlertDialogForLastSeenScreen(lastSeenScreen: String) {
        val dialogBuilder = AlertDialog.Builder(this)
        dialogBuilder.setMessage("")
                ?.setCancelable(false)
                ?.setPositiveButton("Continue") { dialog, id ->

                    ScreenRouter.navigate(lastSeenScreen, mContext)
                }
                ?.setNegativeButton("Restart") { dialog, id ->

                    AuthService.cleanup(this)

                    val intent = Intent(mContext, Intro1Activity::class.java).launchModeWithNoBackStack()
                    mContext.startActivity(intent)
                }
        val alert = dialogBuilder.create()
        alert.setMessage("Would you like to restart the signup process or continue with your last session?")
        alert.show()
    }

    override fun onActivityResult(requestCode: Int,
                                  resultCode: Int,
                                  data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == AppUpdateHelperUtility.REQUEST_CODE_IMMEDIATE_UPDATE) {
            appUpdateHelperUtility.handleOnActivityResult(resultCode)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        this.intent = intent

        // if activity is in foreground (or in backstack but partially visible) launch the same
        // activity will skip onStart, handle this case with reInit
        if (intent.hasExtra("branch_force_new_session") &&
            intent.getBooleanExtra("branch_force_new_session", false)) {
            Branch.sessionBuilder(this).withCallback{ referringParams, error ->
                if (error == null) {
                    parseBranchParams(referringParams)
                } else {
                    Timber.e("BRANCH SDK Error ${error.message}")
                }
            }.reInit()
        }
    }

    private fun initBranch() {
        // Branch init
        Branch.sessionBuilder(this).withCallback { referringParams, error ->
            if (error == null) {
                // Retrieve deeplink keys from 'referringParams' and evaluate the values to determine where to route the user
                // Check '+clicked_branch_link' before deciding whether to use your Branch routing logic
                parseBranchParams(referringParams)
            } else {
                Timber.e("BRANCH SDK Error ${error.message}")
            }
        }.withData(this.intent?.data).init()
    }

    private fun parseBranchParams(referringParams: JSONObject?) {
        val customMetaData = referringParams?.optString(branchCustomMetaKey)
        customMetaData?.let {
            val branchDataMap = getMapFromString(it)
            Timber.d("Testing branch $customMetaData")
            val source = branchDataMap[Constants.source]
            val influencerId = branchDataMap[Constants.influencerId]
            val influencerName = branchDataMap[Constants.influencerName]
            Timber.d("Testing branch source: $source")
            Timber.d("Testing branch influencerId: $influencerId")
            Timber.d("Testing branch influencerName: $influencerName")
            handleBranchCampaignSignups(this@SplashActivity, source, influencerId, influencerName)
        }
    }

    private fun getMapFromString(data: String): Map<String, String> {
        val map = data.split("&").associate {
            val (left, right) = it.split("=")
            left to right
        }
        return map
    }

    /**
     * Handle Meta Ads attribution from app links
     */
    private fun handleMetaAdsAttribution() {
        try {
            val uri = intent?.data
            if (uri != null) {
                Timber.d("Handling app link for Meta Ads attribution: $uri")
                MetaAdsHandler.handleAppLink(this, uri)
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to handle Meta Ads attribution")
        }
    }

    companion object {

        fun newIntent(context: Context): Intent {

            return Intent(context, SplashActivity::class.java)
        }
    }
}
