package com.lovebeats.firebase.database

import android.app.Application
import android.content.Context
import android.location.Location
import android.os.Bundle
import com.firebase.geofire.GeoFire
import com.firebase.geofire.GeoLocation
import com.firebase.geofire.GeoQuery
import com.firebase.geofire.GeoQueryEventListener
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.Tasks
import com.google.firebase.database.ChildEventListener
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.database.ServerValue
import com.google.firebase.database.ValueEventListener
import com.google.firebase.firestore.FieldValue
import com.google.firebase.firestore.SetOptions
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.lovebeats.analytics.ACCOUNT_DELETE
import com.lovebeats.analytics.ANALYTICS_FAILURE
import com.lovebeats.analytics.ANALYTICS_SUCCESS
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.analytics.ERROR_REASON
import com.lovebeats.analytics.MP_API
import com.lovebeats.analytics.MP_API_STATUS
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.analytics.STATUS
import com.lovebeats.deepLinking.PromotionsHandler
import com.lovebeats.glide.MyAppGlideModule
import com.lovebeats.models.ApiKeysConfig
import com.lovebeats.models.SpacesConfig
import com.lovebeats.models.User
import com.lovebeats.models.UserObject
import com.lovebeats.services.AuthService
import com.lovebeats.spaces.SpacesRepository
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Constants.connections
import com.lovebeats.utils.Constants.conversations
import com.lovebeats.utils.Constants.dislikes
import com.lovebeats.utils.Constants.dmRequest
import com.lovebeats.utils.Constants.dmRequestBy
import com.lovebeats.utils.Constants.lCount
import com.lovebeats.utils.Constants.lDate
import com.lovebeats.utils.Constants.likedBy
import com.lovebeats.utils.Constants.likes
import com.lovebeats.utils.Constants.matches
import com.lovebeats.utils.Constants.reportedBy
import com.lovebeats.utils.Constants.unmatchedUsers
import com.lovebeats.utils.Constants.unread
import com.lovebeats.utils.Utils.Companion.getKmsFromMeters
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber

class FirebaseDatabaseUtil(val context: Context) : Application() {

    private var database: DatabaseReference = FirebaseDatabase.getInstance().reference
    private val fireStore = Firebase.firestore
    val mFirebaseUserId: String = AccountPreferences.getInstance(context).getStringValue(Constants.firebaseUserId, "") ?: ""

    val mainUserLocation = Location("mainUserLocation")

    val geoFire = GeoFire(database.child("geofire"))

    fun saveUserInfoToFirebase(userInfoKey: String, userInfoValue: String) {
        if (mFirebaseUserId.isNotEmpty()) {
            val data = hashMapOf(userInfoKey to userInfoValue)
            fireStore.collection(Constants.fireStoreUsers).document(mFirebaseUserId).set(data, SetOptions.merge())

            checkForUpdatedMainUserDataAndSaveInLocal(userInfoKey)
            saveTimeStamp(Constants.modifiedOn)
        }
    }

    fun saveBranchAdCampaignSignupsInFirebase(source: String, phone: String) {
        database.child(Constants.lovebeatPlus).child(phone).child(Constants.source).setValue(source)
    }

    fun saveUserInfoToFirebase(userInfoKey: String, userInfoValue: Double?) {
        if (mFirebaseUserId.isNotEmpty()) {
            val data = hashMapOf(userInfoKey to userInfoValue)
            fireStore.collection(Constants.fireStoreUsers).document(mFirebaseUserId).set(data, SetOptions.merge())

            checkForUpdatedMainUserDataAndSaveInLocal(userInfoKey)
            saveTimeStamp(Constants.modifiedOn)
        }
    }

    fun saveUserInfoToFirebase(userInfoKey: String, userInfoValue: Int) {
        if (mFirebaseUserId.isNotEmpty()) {
            val data = hashMapOf(userInfoKey to userInfoValue)
            fireStore.collection(Constants.fireStoreUsers).document(mFirebaseUserId).set(data, SetOptions.merge())

            checkForUpdatedMainUserDataAndSaveInLocal(userInfoKey)
            saveTimeStamp(Constants.modifiedOn)
        }
    }

    fun saveUserFavoriteInfoToFirebase(type: String, userInfoValue: HashMap<String, List<String>>) {
        if (mFirebaseUserId.isNotEmpty()) {
            fireStore.collection(Constants.fireStoreUsers).document(mFirebaseUserId).update("favorites.$type", userInfoValue)
            checkForUpdatedMainUserDataAndSaveInLocal(Constants.favorites)
            saveTimeStamp(Constants.modifiedOn)
        }
    }

    fun saveUserInfoToFirebase(userInfoKey: String, userInfoValue: Boolean?) {
        if (mFirebaseUserId.isNotEmpty()) {
            val data = hashMapOf(userInfoKey to userInfoValue)
            fireStore.collection(Constants.fireStoreUsers).document(mFirebaseUserId).set(data, SetOptions.merge())

            checkForUpdatedMainUserDataAndSaveInLocal(userInfoKey)
            saveTimeStamp(Constants.modifiedOn)
        }
    }

    fun saveUserInfoToFirebase(userInfoKey: String, userInfoValue: Set<String>) {
        if (mFirebaseUserId.isNotEmpty()) {
            val data = hashMapOf(userInfoKey to userInfoValue.toList())
            fireStore.collection(Constants.fireStoreUsers).document(mFirebaseUserId).set(data, SetOptions.merge())
            checkForUpdatedMainUserDataAndSaveInLocal(userInfoKey)
            saveTimeStamp(Constants.modifiedOn)
        }
    }

    fun saveUserInfoToFirebase(userInfoKey: String, userInfoValue: HashMap<String, String?>) {
        if (mFirebaseUserId.isNotEmpty()) {
            val data = hashMapOf(userInfoKey to userInfoValue)
            fireStore.collection(Constants.fireStoreUsers).document(mFirebaseUserId).set(data, SetOptions.merge())
            checkForUpdatedMainUserDataAndSaveInLocal(userInfoKey)
            saveTimeStamp(Constants.modifiedOn)
        }
    }

    fun updateUserInfoMapToFirebase(
        userInfoKey: String,
        userInfoChildKey: String,
        userInfoChildValue: String
    ) {
        if (mFirebaseUserId.isNotEmpty()) {
            val data = mapOf(userInfoChildKey to userInfoChildValue)
            fireStore.collection(Constants.fireStoreUsers)
                .document(mFirebaseUserId)
                .update(userInfoKey, data)
            checkForUpdatedMainUserDataAndSaveInLocal(userInfoKey)
        }
    }

    fun saveUserCityInfoToFirebase(cityKey: String, cityField: String, cityFieldValue: String) {
        if (mFirebaseUserId.isNotEmpty()) {
            database.child(Constants.cities).child(mFirebaseUserId).child(cityKey).child(cityField)
                .setValue(cityFieldValue)
            saveTimeStamp(Constants.modifiedOn)
        }
    }

    fun saveUserCityInfoToFirebase(cityKey: String, cityField: String, cityFieldValue: Double) {
        if (mFirebaseUserId.isNotEmpty()) {
            database.child(Constants.cities).child(mFirebaseUserId).child(cityKey).child(cityField)
                .setValue(cityFieldValue)
            saveTimeStamp(Constants.modifiedOn)
        }
    }

    fun saveRequestedFavorite(favorite: String) {
        if (mFirebaseUserId.isNotEmpty()) {
            database.child(Constants.requestedFavorites).child(mFirebaseUserId).child(favorite).setValue(true)
        }
    }

    fun readCitiesInfoFromFirebase(retrieveSingleUserListener: FirebaseRetrieveSingleUserListenerInterface) {
        val databaseReference = database.child(Constants.cities).child(mFirebaseUserId)
        databaseReference.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                retrieveSingleUserListener.onFailure()
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {
                retrieveSingleUserListener.onSuccess(dataSnapshot)
            }
        })
    }

    private fun checkForUpdatedMainUserDataAndSaveInLocal(userInfoKey: String) {
        if (UserObject.user?.isOnboardingComplete == true
            && userInfoKey != Constants.lSeen
            && userInfoKey != Constants.city
            && userInfoKey != Constants.state
            && userInfoKey != Constants.zip
            && userInfoKey != Constants.country
            && userInfoKey != Constants.isOnboardingComplete
            && userInfoKey != Constants.freeTrailUsed
        ) {
            readMainUserInfoFromFirebaseFirestore(object :
                FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {
                    UserObject.setUserDataStore(user, mFirebaseUserId)
                }

                override fun onFailure() {
                    Timber.e("failed reading main user info in browse profiles")
                }
            })
        }
    }

    fun saveInfluencerSignupInFirebase(freeTrailUsed: Boolean, context: Context, callback: () -> Unit) {
        val influencerId = AccountPreferences.getInstance(context).getStringValue(Constants.influencerId, "")
        if (!influencerId.isNullOrEmpty()) {

            val source = AccountPreferences.getInstance(context).getStringValue(com.lovebeats.utils.Constants.source, "")
            val phone = AccountPreferences.getInstance(context).getStringValue(Constants.phoneNumber, "")
            val email = AccountPreferences.getInstance(context).getStringValue(com.lovebeats.utils.Constants.email, "")
            val influencerName = AccountPreferences.getInstance(context).getStringValue(com.lovebeats.utils.Constants.influencerName, "")
            var phoneOrEmail = phone
            if (phoneOrEmail.isEmpty()) {
                phoneOrEmail = email
            }

            database.child(Constants.influencerSignups).child(influencerId).child(phoneOrEmail).child(Constants.source).setValue(source)
            database.child(Constants.influencerSignups).child(influencerId).child(phoneOrEmail).child(Constants.influencerName).setValue(influencerName)
            database.child(Constants.influencerSignups).child(influencerId).child(phoneOrEmail).child(Constants.timestamp).setValue(ServerValue.TIMESTAMP)

            saveUserInfoToFirebase(Constants.freeTrailUsed, freeTrailUsed)
            AccountPreferences.getInstance(context).setValue(Constants.freeTrailUsed, freeTrailUsed)
            callback()
        }
    }

    fun saveInfluencerSignupInFirebaseAndResetInPrefs(freeTrailUsed: Boolean, context: Context) {
        saveInfluencerSignupInFirebase(freeTrailUsed, context) {
            PromotionsHandler.resetInfluencerSignups(context)
        }
    }

    fun saveLastSeen() {
        if (mFirebaseUserId.isNotEmpty()) {
            val data = hashMapOf(Constants.lSeen to FieldValue.serverTimestamp())
            fireStore.collection(Constants.fireStoreUsers).document(mFirebaseUserId).set(data, SetOptions.merge())
        }
    }

    fun saveLastScreen(screenName: String) {
        if (mFirebaseUserId.isNotEmpty()) {
            saveUserInfoToFirebase(Constants.lScreen, screenName)
        }
    }

    fun saveTimeStamp(userInfoKey: String) {

        if (mFirebaseUserId.isNotEmpty()) {
            val data = hashMapOf(userInfoKey to FieldValue.serverTimestamp())
            fireStore.collection(Constants.fireStoreUsers).document(mFirebaseUserId).set(data, SetOptions.merge())
        }
    }

    fun readLikeCounter(date: String, callback: (Int) -> Unit) {
        val dbRef = database.child(Constants.likeCounter).child(mFirebaseUserId)
        dbRef.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                callback(0)
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {
                if (dataSnapshot.value != null) {
                    val likesCounterMap = dataSnapshot.value as? HashMap<String, Any>
                    if (likesCounterMap != null && likesCounterMap.size > 0) {
                        if (dataSnapshot.hasChild(lCount)) {
                            val likeCounterDate = likesCounterMap[lDate] as? String
                            val likeCounterCount = likesCounterMap[lCount] as? Long
                            if (likeCounterDate == date) {
                                callback((likeCounterCount ?: 0).toInt())
                            } else {
                                writeLikeCounter(date, 0)
                                callback(0)
                            }
                        } else {
                            writeLikeCounter(date, 0)
                            callback(0)
                        }
                    } else {
                        writeLikeCounter(date, 0)
                        callback(0)
                    }
                } else {
                    writeLikeCounter(date, 0)
                    callback(0)
                }
            }
        })
    }

    fun writeLikeCounter(date: String, count: Int) {
        database.child(Constants.likeCounter).child(mFirebaseUserId).child(lDate).setValue(date)
        database.child(Constants.likeCounter).child(mFirebaseUserId).child(lCount).setValue(count)
    }

    /*
    This method checks subscription status from firebase database.
    This implementation is not complete. It should get isActive and expiredTimeInMillis fields and
    check if the date is not past. Then only the user has active subscription.
     */
    fun checkSubscriptionStatus(callback: (Boolean) -> Unit) {
        val dbRef = database.child(Constants.lovebeatPlus).child(mFirebaseUserId)
        dbRef.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                callback(false)
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {
                if (dataSnapshot.value != null && dataSnapshot.value is Boolean) {
                    callback((dataSnapshot.value as Boolean))
                } else {
                    callback(false)
                }
            }
        })
    }

    fun clearAndSaveChildUserInfoToFirebase(
        userInfoKey: String,
        userInfoChildKey: String,
        userInfoChildValue: String
    ) {
        if (mFirebaseUserId.isNotEmpty()) {
            val data = hashMapOf(userInfoKey to mapOf(userInfoChildKey to userInfoChildValue))
            fireStore.collection(Constants.fireStoreUsers)
                .document(mFirebaseUserId)
                .set(data, SetOptions.merge())

            checkForUpdatedMainUserDataAndSaveInLocal(userInfoKey)
        }
    }

    fun readMainUserInfoFromFirebaseFirestore(retrieveSingleUserListener: FirebaseRetrieveSingleUserListenerInterfaceFirestore) {
        var firebaseId = mFirebaseUserId
        if (firebaseId.isEmpty()) {
            firebaseId = AccountPreferences.getInstance(context).getStringValue(Constants.firebaseUserId, "")
        }

        val docRef = fireStore.collection(Constants.fireStoreUsers).document(firebaseId)
        docRef.get()
            .addOnSuccessListener { document ->
                if (document != null && document.exists()) {
                    val user = document.toObject(User::class.java)
                    if (user != null) {
                        retrieveSingleUserListener.onSuccess(user)
                    } else {
                        retrieveSingleUserListener.onFailure()
                    }
                } else {
                    retrieveSingleUserListener.onFailure()
                }
            }
            .addOnFailureListener {
                retrieveSingleUserListener.onFailure()
            }
    }

    fun readMainUserMapRawDataFromFirebaseFirestore(retrieveSingleUserListener: FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore, firebaseId: String = mFirebaseUserId) {
        val docRef = fireStore.collection(Constants.fireStoreUsers).document(firebaseId)
        docRef.get()
            .addOnSuccessListener { document ->
                retrieveSingleUserListener.onSuccess(document)
            }
            .addOnFailureListener {
                retrieveSingleUserListener.onFailure()
            }
    }

    fun readSingleUserInfoFromFirebaseFirestore(retrieveSingleUserListener: FirebaseRetrieveSingleUserListenerInterfaceFirestore,
                                                nodeId: String
    ) {
        val docRef = fireStore.collection(Constants.fireStoreUsers).document(nodeId)
        docRef.get()
            .addOnSuccessListener { document ->
                if (document != null && document.exists()) {
                    val user = document.toObject(User::class.java)
                    if (user != null) {
                        retrieveSingleUserListener.onSuccess(user)
                    } else {
                        retrieveSingleUserListener.onFailure()
                    }
                } else {
                    retrieveSingleUserListener.onFailure()
                }
            }
            .addOnFailureListener {
                retrieveSingleUserListener.onFailure()
            }
    }

    fun readMainUserConnectionsFromFirebase(retrieveSingleUserListener: FirebaseRetrieveSingleUserListenerInterface) {

        val connectionsReference = database.child(connections).child(mFirebaseUserId)
        connectionsReference.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                retrieveSingleUserListener.onFailure()
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {
                retrieveSingleUserListener.onSuccess(dataSnapshot)
            }
        })
    }

    fun readMainUserInfoFromFirebaseRealtimeDatabase(retrieveSingleUserListener: FirebaseRetrieveSingleUserListenerInterface) {

        val singleUserDatabaseReference = database.child(Constants.users).child(mFirebaseUserId)

        singleUserDatabaseReference.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                retrieveSingleUserListener.onFailure()
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {
                retrieveSingleUserListener.onSuccess(dataSnapshot)
            }
        })
    }

    fun readUnreadStatus(unreadStatusListener: FirebaseUnreadStatusListener) {

        val connectionsReference = database.child(connections)
            .child(mFirebaseUserId)
            .child(matches)

        connectionsReference.addValueEventListener(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                unreadStatusListener.onFailure()
                connectionsReference.removeEventListener(this)
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {

                var matchesMap: HashMap<String, Any>? = HashMap()

                if (dataSnapshot.value != null) {

                    matchesMap = dataSnapshot.value as? HashMap<String, Any>
                }

                if (matchesMap != null && matchesMap.size > 0) {

                    for ((key, value) in matchesMap) {

                        if (dataSnapshot.hasChild("$key/$unread") || dataSnapshot.hasChild("$key/$dmRequest")) {
                            val matchesMapDetails = value as? HashMap<*, *>
                            val unread = matchesMapDetails?.get(unread) as? Boolean
                            val dmRequest = matchesMapDetails?.get(dmRequest) as? Boolean
                            val dmRequestBy = matchesMapDetails?.get(dmRequestBy) as? String

                            val unreadStatus = unread == true || (dmRequest == true && dmRequestBy != mFirebaseUserId)
                            if (unreadStatus) {
                                unreadStatusListener.onSuccess(
                                    true,
                                    connectionsReference,
                                    this
                                )
                                break
                            } else {
                                unreadStatusListener.onSuccess(
                                    false,
                                    connectionsReference,
                                    this
                                )
                            }
                        }
                    }
                }
            }
        })
    }

    fun readAllMatchedUsersInfoFromFirebase(retrieveAllMatchedUsersListener: FirebaseRetrieveAllMatchesListenerInterface) {

        retrieveAllMatchedUsersListener.onStart()

        val connectionsReference = database.child(connections).child(mFirebaseUserId).child(matches)

        connectionsReference.addListenerForSingleValueEvent(object : ValueEventListener {

            override fun onCancelled(p0: DatabaseError) {

                retrieveAllMatchedUsersListener.onFailure()
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {

                retrieveAllMatchedUsersListener.onSuccess(dataSnapshot)
            }
        })
    }

    fun getUnmatchedUsersListener(
        retrieveAllMatchedUsersListener: FirebaseRetrieveChildListener,
        nodeId: String
    ) {

        val connectionsReference = database.child(connections).child(nodeId).child(unmatchedUsers)

        connectionsReference.addChildEventListener(object : ChildEventListener {

            override fun onCancelled(p0: DatabaseError) {
                retrieveAllMatchedUsersListener.onFailure()
                connectionsReference.removeEventListener(this)
            }

            override fun onChildMoved(dataSnapshot: DataSnapshot, p1: String?) {
                retrieveAllMatchedUsersListener.onChildMoved(
                    dataSnapshot,
                    p1,
                    connectionsReference,
                    this
                )
            }

            override fun onChildChanged(dataSnapshot: DataSnapshot, p1: String?) {
                retrieveAllMatchedUsersListener.onChildChanged(
                    dataSnapshot,
                    p1,
                    connectionsReference,
                    this
                )
            }

            override fun onChildAdded(dataSnapshot: DataSnapshot, p1: String?) {
                retrieveAllMatchedUsersListener.onChildAdded(
                    dataSnapshot,
                    p1,
                    connectionsReference,
                    this
                )
            }

            override fun onChildRemoved(dataSnapshot: DataSnapshot) {
                retrieveAllMatchedUsersListener.onChildRemoved(
                    dataSnapshot,
                    connectionsReference,
                    this
                )
            }
        })
    }

    fun readAllMatchedUsersInfoFromFirebaseChildListener(retrieveAllMatchedUsersListener: FirebaseRetrieveChildListener) {

        val connectionsReference = database.child(connections).child(mFirebaseUserId).child(matches)

        connectionsReference.addChildEventListener(object : ChildEventListener {

            override fun onCancelled(p0: DatabaseError) {
                retrieveAllMatchedUsersListener.onFailure()
                connectionsReference.removeEventListener(this)
            }

            override fun onChildMoved(dataSnapshot: DataSnapshot, p1: String?) {
                retrieveAllMatchedUsersListener.onChildMoved(
                    dataSnapshot,
                    p1,
                    connectionsReference,
                    this
                )
            }

            override fun onChildChanged(dataSnapshot: DataSnapshot, p1: String?) {
                retrieveAllMatchedUsersListener.onChildChanged(
                    dataSnapshot,
                    p1,
                    connectionsReference,
                    this
                )
            }

            override fun onChildAdded(dataSnapshot: DataSnapshot, p1: String?) {
                retrieveAllMatchedUsersListener.onChildAdded(
                    dataSnapshot,
                    p1,
                    connectionsReference,
                    this
                )
            }

            override fun onChildRemoved(dataSnapshot: DataSnapshot) {
                retrieveAllMatchedUsersListener.onChildRemoved(
                    dataSnapshot,
                    connectionsReference,
                    this
                )
            }
        })
    }

    fun readNearbyUsersInfoFromFirebaseGeofire(
        geoQuery: GeoQuery,
        isPaginating: Boolean,
        mainUserLatitude: Double,
        mainUserLongitude: Double,
        retrieveNearbyUsersListener: FirebaseRetrieveNearbyUsersListenerInterface
    ) {
        val nearByUsersKeysLinkedHashMap: LinkedHashMap<String, Location> = LinkedHashMap()
        val nearByUsersHashMap: LinkedHashMap<String, User?> = LinkedHashMap()
        val nearByUsersFetchStatus = mutableSetOf("")

        geoQuery.addGeoQueryEventListener(object : GeoQueryEventListener {
            override fun onGeoQueryReady() {
                //https://github.com/firebase/geofire-js/issues/59

                val usersDistanceMap: LinkedHashMap<String, Int> = LinkedHashMap()
                var usersSortedByDistanceLinkedHashMap: LinkedHashMap<String, User?> = LinkedHashMap()
                val taskList = mutableListOf<Task<DataSnapshot>>()

                mainUserLocation.latitude = mainUserLatitude
                mainUserLocation.longitude = mainUserLongitude

                if (nearByUsersKeysLinkedHashMap.isNotEmpty()) {

                    for ((key, value ) in nearByUsersKeysLinkedHashMap) {
                        if (nearByUsersFetchStatus.contains(key)) {
                            continue
                        }
                        val distanceBetweenUsers = getKmsFromMeters(mainUserLocation.distanceTo(value).toInt())
                        usersDistanceMap[key] = distanceBetweenUsers

                        nearByUsersFetchStatus.add(key)

                        val databaseReferenceTask: Task<DataSnapshot> = database.child(Constants.users).child(key).get()
                        taskList.add(databaseReferenceTask)
                    }

                    val resultTask = Tasks.whenAll(taskList)
                    resultTask.addOnCompleteListener {
                        for (task in taskList) {
                            val snapshotKey: String? = task.result.key
                            val snapShotValue = task.result
                            if (snapshotKey != null && snapShotValue != null) {
                                if (snapShotValue.getValue(User::class.java) != null && snapshotKey != mFirebaseUserId) {
                                    nearByUsersHashMap[snapshotKey] = snapShotValue.getValue(User::class.java)
                                }
                            }
                        }

                        usersSortedByDistanceLinkedHashMap = sortUsersByDistance(nearByUsersHashMap, usersDistanceMap)
                        retrieveNearbyUsersListener.onSuccess(usersSortedByDistanceLinkedHashMap, usersDistanceMap)
                    }

                    if (!isPaginating) {
                        geoQuery.removeAllListeners()
                    }
                } else {
                    retrieveNearbyUsersListener.onFailure()
                }
            }

            override fun onKeyEntered(key: String?, location: GeoLocation?) {
                if (key != null) {

                    val otherUserLocation = Location("geofire-location")
                    if (location != null) {
                        otherUserLocation.latitude = location.latitude
                        otherUserLocation.longitude = location.longitude
                    }

                    nearByUsersKeysLinkedHashMap[key] = otherUserLocation
                }
            }

            override fun onKeyMoved(key: String?, location: GeoLocation?) {
            }

            override fun onKeyExited(key: String?) {
            }

            override fun onGeoQueryError(error: DatabaseError?) {
                retrieveNearbyUsersListener.onFailure()
                geoQuery.removeAllListeners()
            }
        })
    }

    private fun sortUsersByDistance(
        originalUsersHashMap: LinkedHashMap<String, User?>,
        originalDistanceHashMap: LinkedHashMap<String, Int>
    ): LinkedHashMap<String, User?> {
        val sortedDistanceLinkedHashMap: LinkedHashMap<String, Int> =
            (originalDistanceHashMap.entries.sortedBy { it.value }
                .associate { it.toPair() } as? LinkedHashMap<String, Int>) ?: LinkedHashMap()
        val sortedUsersByDistanceLinkedHashMap: LinkedHashMap<String, User?> = LinkedHashMap()
        for ((key, _) in sortedDistanceLinkedHashMap) {
            val user: User? = originalUsersHashMap[key]
            sortedUsersByDistanceLinkedHashMap[key] = user
        }
        return sortedUsersByDistanceLinkedHashMap
    }

    fun readChatInfoFromFirebase(
        retrieveChatInfoListener: FirebaseRetrieveChatInfo,
        nodeId: String
    ) {

        val chatInfoDatabaseReference = database.child(Constants.conversations).child(nodeId)

        val sortQuery = chatInfoDatabaseReference.orderByChild(Constants.timestamp)

        sortQuery.addChildEventListener(object : ChildEventListener {
            override fun onCancelled(p0: DatabaseError) {
                retrieveChatInfoListener.onFailure()
            }

            override fun onChildMoved(p0: DataSnapshot, p1: String?) {
            }

            override fun onChildChanged(p0: DataSnapshot, p1: String?) {
                //retrieveChatInfoListener.onSuccess(p0)
            }

            override fun onChildAdded(p0: DataSnapshot, p1: String?) {
                retrieveChatInfoListener.onSuccess(p0)
            }

            override fun onChildRemoved(p0: DataSnapshot) {
            }
        })
    }

    fun readMessageUnreadStatusFromFirebase(
        retrieveChatInfoListener: FirebaseRetrieveChildListenerGeneric,
        otherUserId: String,
        mainUserId: String
    ) {

        val chatInfoDatabaseReference = database.child(connections).child(otherUserId).child(matches).child(mainUserId)

        chatInfoDatabaseReference.addValueEventListener(object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                retrieveChatInfoListener.onSuccess(snapshot)
            }

            override fun onCancelled(error: DatabaseError) {
            }
        })
    }

    fun getLikedByUsers(
        likedByUsersListener: FirebaseRetrieveLikedByUsersListener,
        mainUserId: String,
        otherUserId: String
    ) {

        val connectionsReference = database.child(connections).child(mainUserId).child(likedBy)

        connectionsReference.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {

                likedByUsersListener.onFailure()
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {

                if (dataSnapshot.hasChild(otherUserId)) {
                    likedByUsersListener.onSuccess(true)
                } else {
                    likedByUsersListener.onFailure()
                }
            }
        })
    }

    fun getAllLikedByUsersList(
        likedByUsersListener: FirebaseGetAllLikedByUsersListListener,
        mainUserId: String
    ) {

        val connectionsReference = database.child(connections).child(mainUserId).child(likedBy)

        connectionsReference.addListenerForSingleValueEvent(object : ValueEventListener {

            override fun onCancelled(p0: DatabaseError) {

                likedByUsersListener.onFailure()
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {

                filterLikedByUsers(dataSnapshot) {

                    likedByUsersListener.onSuccess(it)
                }
            }
        })
    }

    fun likeUser(mainUserId: String, otherUserId: String) {

        val connectionsReference = database.child(connections)
        connectionsReference.child(mainUserId).child(likes).child(otherUserId).setValue(true)
        connectionsReference.child(otherUserId).child(likedBy).child(mainUserId)
            .setValue(ServerValue.TIMESTAMP)
    }

    fun disLikeUser(mainUserId: String, otherUserId: String) {

        val connectionsReference = database.child(connections)
        connectionsReference.child(mainUserId).child(dislikes).child(otherUserId)
            .setValue(true)
        connectionsReference.child(otherUserId).child(Constants.dislikedBy).child(mainUserId)
            .setValue(true)
    }

    fun matchUsers(mainUserFirebaseId: String, otherUserFirebaseId: String, chatId: String?, dmRequest: Boolean = false, message: String = "", dmRequestBy: String? = null) {
        if (dmRequest) {
            // only for dmRequest, should check if there is already a chatId in the server and use that instead of creating a new one like firebaseDatabaseReference.key
            checkExistingChatIdAndMatch(mainUserFirebaseId, otherUserFirebaseId, message, dmRequestBy)
            return
        }

        val connectionsReference = database.child(connections)
        connectionsReference.child(mainUserFirebaseId).child(matches)
            .child(otherUserFirebaseId).child(Constants.timestamp).setValue(ServerValue.TIMESTAMP)
        connectionsReference.child(mainUserFirebaseId).child(matches)
            .child(otherUserFirebaseId).child(Constants.chatId).setValue(chatId)
        connectionsReference.child(mainUserFirebaseId).child(matches)
            .child(otherUserFirebaseId).child(Constants.dmRequest).setValue(dmRequest)

        if (message.isNotEmpty()) {
            connectionsReference.child(mainUserFirebaseId).child(matches)
                .child(otherUserFirebaseId).child(Constants.message).setValue(message)
        }

        if (dmRequestBy != null) {
            connectionsReference.child(mainUserFirebaseId).child(matches)
                .child(otherUserFirebaseId).child(Constants.dmRequestBy).setValue(dmRequestBy)
        }

        connectionsReference.child(otherUserFirebaseId).child(matches)
            .child(mainUserFirebaseId).child(Constants.timestamp).setValue(ServerValue.TIMESTAMP)
        connectionsReference.child(otherUserFirebaseId).child(matches)
            .child(mainUserFirebaseId).child(Constants.chatId).setValue(chatId)
        connectionsReference.child(otherUserFirebaseId).child(matches)
            .child(mainUserFirebaseId).child(Constants.dmRequest).setValue(dmRequest)

        if (message.isNotEmpty()) {
            connectionsReference.child(otherUserFirebaseId).child(matches)
                .child(mainUserFirebaseId).child(Constants.message).setValue(message)
        }

        if (dmRequestBy != null) {
            connectionsReference.child(otherUserFirebaseId).child(matches)
                .child(mainUserFirebaseId).child(Constants.dmRequestBy).setValue(dmRequestBy)
        }
    }

    private fun checkExistingChatIdAndMatch(mainUserFirebaseId: String, otherUserFirebaseId: String, message: String, dmRequestBy: String?) {
        // Check if there's already a match between these users
        val connectionsReference = database.child(connections).child(mainUserFirebaseId).child(matches).child(otherUserFirebaseId)

        connectionsReference.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onDataChange(dataSnapshot: DataSnapshot) {
                var existingChatId: String? = null

                if (dataSnapshot.exists() && dataSnapshot.hasChild(Constants.chatId)) {
                    // There's already a match with a chatId, use the existing one
                    existingChatId = dataSnapshot.child(Constants.chatId).getValue(String::class.java)
                }

                if (existingChatId.isNullOrEmpty()) {
                    // No existing chatId found, create a new one
                    val firebaseDatabaseReference = FirebaseDatabase.getInstance().getReference(conversations).push()
                    existingChatId = firebaseDatabaseReference.key
                }

                // Now proceed with the matching using the existing or new chatId
                proceedWithDmMatch(mainUserFirebaseId, otherUserFirebaseId, existingChatId, message, dmRequestBy)
            }

            override fun onCancelled(error: DatabaseError) {
                // If there's an error, create a new chatId as fallback
                val firebaseDatabaseReference = FirebaseDatabase.getInstance().getReference(conversations).push()
                val newChatId = firebaseDatabaseReference.key
                proceedWithDmMatch(mainUserFirebaseId, otherUserFirebaseId, newChatId, message, dmRequestBy)
            }
        })
    }

    private fun proceedWithDmMatch(mainUserFirebaseId: String, otherUserFirebaseId: String, chatId: String?, message: String, dmRequestBy: String?) {
        val connectionsReference = database.child(connections)

        // Set match data for main user
        connectionsReference.child(mainUserFirebaseId).child(matches)
            .child(otherUserFirebaseId).child(Constants.timestamp).setValue(ServerValue.TIMESTAMP)
        connectionsReference.child(mainUserFirebaseId).child(matches)
            .child(otherUserFirebaseId).child(Constants.chatId).setValue(chatId)
        connectionsReference.child(mainUserFirebaseId).child(matches)
            .child(otherUserFirebaseId).child(Constants.dmRequest).setValue(true)

        if (message.isNotEmpty()) {
            connectionsReference.child(mainUserFirebaseId).child(matches)
                .child(otherUserFirebaseId).child(Constants.message).setValue(message)
        }

        if (dmRequestBy != null) {
            connectionsReference.child(mainUserFirebaseId).child(matches)
                .child(otherUserFirebaseId).child(Constants.dmRequestBy).setValue(dmRequestBy)
        }

        // Set match data for other user
        connectionsReference.child(otherUserFirebaseId).child(matches)
            .child(mainUserFirebaseId).child(Constants.timestamp).setValue(ServerValue.TIMESTAMP)
        connectionsReference.child(otherUserFirebaseId).child(matches)
            .child(mainUserFirebaseId).child(Constants.chatId).setValue(chatId)
        connectionsReference.child(otherUserFirebaseId).child(matches)
            .child(mainUserFirebaseId).child(Constants.dmRequest).setValue(true)

        if (message.isNotEmpty()) {
            connectionsReference.child(otherUserFirebaseId).child(matches)
                .child(mainUserFirebaseId).child(Constants.message).setValue(message)
        }

        if (dmRequestBy != null) {
            connectionsReference.child(otherUserFirebaseId).child(matches)
                .child(mainUserFirebaseId).child(Constants.dmRequestBy).setValue(dmRequestBy)
        }
    }

    fun setDmRejected(mainUserFirebaseId: String, otherUserFirebaseId: String) {
        val connectionsReference = database.child(connections)
        connectionsReference.child(mainUserFirebaseId).child(matches)
            .child(otherUserFirebaseId).child(Constants.dmRequestRejected).setValue(true)
        connectionsReference.child(otherUserFirebaseId).child(matches)
            .child(mainUserFirebaseId).child(Constants.dmRequestRejected).setValue(true)
    }

    fun removeDislike(mainUserId: String, otherUserId: String) {
        val connectionsReference = database.child(connections)
        connectionsReference.child(mainUserId).child(dislikes).child(otherUserId).removeValue()
        connectionsReference.child(otherUserId).child(Constants.dislikedBy).child(mainUserId).removeValue()
    }

    fun filterLikedByUsers(likedByUsersData: DataSnapshot, callback: (ArrayList<String>) -> Unit) {

        val likedByUsersArrayList = arrayListOf<String>()

        if (likedByUsersData.exists()) {

            readMainUserConnectionsFromFirebase(object :
                FirebaseRetrieveSingleUserListenerInterface {

                override fun onSuccess(dataSnapshot: DataSnapshot) {

                    val usersMap = likedByUsersData.value as? HashMap<String, Any>

                    if (!usersMap.isNullOrEmpty() && dataSnapshot.exists()) {

                        var index = 1

                        for (key in usersMap.keys) {

                            if (!dataSnapshot.child(matches).hasChild(key) &&
                                !dataSnapshot.child(likes).hasChild(key) &&
                                !dataSnapshot.child(unmatchedUsers).hasChild(key)
                            ) {

                                likedByUsersArrayList.add(key)
                            }

                            if (index == usersMap.size) {

                                callback(likedByUsersArrayList)
                                return
                            }

                            index += 1
                        }
                    } else {

                        callback(likedByUsersArrayList)
                    }
                }

                override fun onFailure() {

                    callback(likedByUsersArrayList)
                }
            })
        } else {

            callback(likedByUsersArrayList)
        }
    }

    fun saveLocationToGeofire(lat: Double, long: Double) {

        try {
            if (mFirebaseUserId.isNotEmpty()) {

                geoFire.setLocation(mFirebaseUserId, GeoLocation(lat, long)) { key, error ->

                    if (error != null) {

                        Timber.e("There was an error saving the location to GeoFire: $error")
                    } else {

                        UserObject.isLocationSentToServer = true
                    }
                }
            }
        } catch (e: Exception) {

            Timber.e("error in saving geo location: $e")
        }
    }

    fun deleteUnmatchedUserFromMainUserNode(otherUserId: String?, callback: () -> Unit) {
        if (!otherUserId.isNullOrEmpty()) {
            database.child(connections).child(mFirebaseUserId).child(matches)
                .child(otherUserId).removeValue().addOnCompleteListener {
                    callback()
                }
        }
    }

    fun deleteAccountUsingCloudFunction(callback: (Boolean) -> Unit) {
        var firebaseId = mFirebaseUserId
        if (mFirebaseUserId == "") {
            firebaseId = AccountPreferences.getInstance(context).getStringValue(Constants.firebaseUserId, "")
        }
        val data = hashMapOf(
            "uid" to firebaseId
        )
        if (!firebaseId.isNullOrEmpty()) {
            Firebase.functions
                .getHttpsCallable("deleteUserData")
                .call(data)
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        Timber.d("success in deleting main auth user")
                        for (pathIndex in 1 until 7) {
                            val imagePath = "$firebaseId/${pathIndex}.webp"
                            val spacesRepository = SpacesRepository()
                            GlobalScope.launch {
                                spacesRepository.delete(imagePath) {}
                            }
                        }

                        AuthService.cleanup(context)

                        val bundle = Bundle()
                        bundle.putString(STATUS, ANALYTICS_SUCCESS)
                        AnalyticsTrackingService.logEvent(context, ACCOUNT_DELETE, bundle)

                        val map = hashMapOf<String, String>()
                        map[MP_API] = ACCOUNT_DELETE
                        map[STATUS] = ANALYTICS_SUCCESS
                        MixPanelAnalyticsTrackingService.logEvent(context, MP_API_STATUS, map)

                        AnalyticsTrackingService.resetUserProperties(context)
                        callback(true)
                    } else {
                        val bundle = Bundle()
                        bundle.putString(STATUS, ANALYTICS_FAILURE)
                        bundle.putString(ERROR_REASON, task.exception.toString())
                        AnalyticsTrackingService.logEvent(context, ACCOUNT_DELETE, bundle)

                        val map = hashMapOf<String, String>()
                        map[MP_API] = ACCOUNT_DELETE
                        map[STATUS] = ANALYTICS_FAILURE
                        map[ERROR_REASON] = task.exception.toString()
                        MixPanelAnalyticsTrackingService.logEvent(context, MP_API_STATUS, map)

                        Timber.d("failure in deleting main auth user: $task")
                        callback(false)
                    }
                }
            MyAppGlideModule.clearCache(context)
        }
    }

    fun readUserGeoFireInfo(
        retrieveSingleUserListener: FirebaseRetrieveSingleUserListenerInterface,
        nodeId: String
    ) {

        val geofireUserDatabaseReference = database.child(Constants.geofire).child(nodeId)

        geofireUserDatabaseReference.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                retrieveSingleUserListener.onFailure()
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {

                retrieveSingleUserListener.onSuccess(dataSnapshot)
            }
        })
    }

    fun getDistanceBetweenTwoUsers(user1FirebaseId: String, user2FirebaseId: String, callback: (Int?) -> Unit) {

        var distanceBetweenUsers = 0

        readUserGeoFireInfo(object : FirebaseRetrieveSingleUserListenerInterface {

            override fun onSuccess(dataSnapshot: DataSnapshot) {

                if (dataSnapshot.exists()) {

                    val locationMap = dataSnapshot.value as HashMap<Any, Any>
                    val locationValues = locationMap["l"] as List<Double>

                    val user1Location = Location("user1Location")
                    user1Location.latitude = locationValues[0]
                    user1Location.longitude = locationValues[1]

                    readUserGeoFireInfo(object : FirebaseRetrieveSingleUserListenerInterface {

                        override fun onSuccess(dataSnapshot2: DataSnapshot) {

                            if (dataSnapshot2.exists()) {

                                val locationMap2 = dataSnapshot2.value as HashMap<Any, Any>
                                val locationValues2 = locationMap2["l"] as List<Double>

                                val user2Location = Location("user2Location")
                                user2Location.latitude = locationValues2[0]
                                user2Location.longitude = locationValues2[1]

                                distanceBetweenUsers = getKmsFromMeters(
                                    user1Location.distanceTo(user2Location).toInt()
                                )
                                callback(distanceBetweenUsers)
                            }else {
                                callback(null)
                            }
                        }

                        override fun onFailure() {
                            callback(null)
                        }
                    }, user2FirebaseId)
                }
            }

            override fun onFailure() {
                callback(null)
            }
        }, user1FirebaseId)
    }

    fun unMatchUsers(mainUserId: String, otherUserId: String, reason: String, callback: () -> Unit) {
        if (mainUserId.isNotEmpty() && otherUserId.isNotEmpty()) {
            database.child(connections).child(mainUserId)
                .child(matches).child(otherUserId).removeValue()

            database.child(connections).child(otherUserId)
                .child(matches).child(mainUserId).removeValue()

            database.child(connections).child(mainUserId)
                .child(unmatchedUsers).child(otherUserId).setValue(reason).addOnCompleteListener {
                    callback()
                }
        }
    }

    fun reportUser(mainUserId: String, otherUserId: String, reason: String) {

        if (mainUserId.isNotEmpty() and otherUserId.isNotEmpty()) {
            database.child(Constants.reportedUsers).child(otherUserId).child(reportedBy)
                .child(mainUserId).setValue(reason)
        }
    }

    fun getSpacesConfigs(firebaseGenericStatusListener: FirebaseGenericStatusListener? = null) {
        val spacesConfigsRef = database.child(Constants.spacesConfigs)
        spacesConfigsRef.keepSynced(true)
        spacesConfigsRef.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                firebaseGenericStatusListener?.onFailure()
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {
                if (dataSnapshot.getValue(SpacesConfig::class.java) != null) {
                    saveSpacesConfigs(dataSnapshot)
                    firebaseGenericStatusListener?.onSuccess()
                } else {
                    firebaseGenericStatusListener?.onFailure()
                }
            }
        })
    }

    fun getApiKeys(firebaseGenericStatusListener: FirebaseGenericStatusListener? = null) {
        val apiKeysConfig = database.child(Constants.apiKeys)
        apiKeysConfig.keepSynced(true)
        apiKeysConfig.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                firebaseGenericStatusListener?.onFailure()
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {
                if (dataSnapshot.getValue(ApiKeysConfig::class.java) != null) {
                    saveApiKeys(dataSnapshot)
                    firebaseGenericStatusListener?.onSuccess()
                } else {
                    firebaseGenericStatusListener?.onFailure()
                    Timber.e("Failure in parsing API keys")
                }
            }
        })
    }

    fun saveSpacesConfigs(dataSnapshot: DataSnapshot) {
        val spacesConfig: SpacesConfig? = dataSnapshot.getValue(SpacesConfig::class.java)
        SPACES_UPLOAD_ENDPOINT = spacesConfig?.uUrl ?: ""
        SPACES_DOWNLOAD_ENDPOINT = spacesConfig?.dUrl ?: ""
        SPACES_BUCKET_NAME = spacesConfig?.bucket ?: ""
        SPACES_REGION_NAME = spacesConfig?.region ?: ""
        SPACES_ACCESS_KEY = spacesConfig?.access ?: ""
        SPACES_SECRET_KEY = spacesConfig?.secret ?: ""
    }

    fun saveApiKeys(dataSnapshot: DataSnapshot) {
        val apiKeysConfig: ApiKeysConfig? = dataSnapshot.getValue(ApiKeysConfig::class.java)
        GOOGLE_PLACES_API_KEY = apiKeysConfig?.places ?: ""
    }

    companion object {
        var SPACES_UPLOAD_ENDPOINT = ""
        var SPACES_DOWNLOAD_ENDPOINT = ""
        var SPACES_BUCKET_NAME = ""
        var SPACES_REGION_NAME = ""
        var SPACES_ACCESS_KEY = ""
        var SPACES_SECRET_KEY = ""

        var GOOGLE_PLACES_API_KEY = ""

        fun isGooglePlacesKeyValid(): Boolean {
            return GOOGLE_PLACES_API_KEY.isNotEmpty()
        }
    }
}
