package com.lovebeats

import android.app.Application
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.ProcessLifecycleOwner
import com.facebook.FacebookSdk
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.Firebase
import com.google.firebase.appcheck.appCheck
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory
import com.google.firebase.initialize
import com.lovebeats.firebase.cloudFunctions.NumLikesRestriction
import com.lovebeats.firebase.config.RemoteConfigurationService
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.services.AuthService
import com.lovebeats.spaces.SpacesRepository
import com.lovebeats.storage.room.AppDatabase
import com.lovebeats.storage.room.LocalDataSource
import com.lovebeats.subscriptions.billing.BillingClientLifecycle
import com.lovebeats.subscriptions.network.ServerFunctionsImpl
import com.lovebeats.subscriptions.repo.DataRepository
import com.lovebeats.utils.TimberLogger
import com.lovebeats.analytics.InstallReferrerHandler
import com.lovebeats.analytics.MetaAdsHandler
import com.lovebeats.utils.EdgeToEdgeUtils
import io.branch.referral.Branch
import timber.log.Timber

class LoveBeatApplication : Application(), LifecycleObserver {

    private val database: AppDatabase
        get() = AppDatabase.getInstance(this)

    private val localDataSource: LocalDataSource
        get() = LocalDataSource.getInstance(database)

    private val serverFunctions: ServerFunctionsImpl
        get() {
            return ServerFunctionsImpl()
        }

    val billingClientLifecycle: BillingClientLifecycle
        get() = BillingClientLifecycle.getInstance(this)

    val repository: DataRepository
        get() = DataRepository.getInstance(serverFunctions, localDataSource)

    override fun onCreate() {
        super.onCreate()

        TimberLogger()

        Timber.d("Application Started")

        Firebase.initialize(context = this)
        Firebase.appCheck.installAppCheckProviderFactory(
            PlayIntegrityAppCheckProviderFactory.getInstance(),
        )

        // Initialize Facebook SDK
        FacebookSdk.sdkInitialize(this)
        AppEventsLogger.activateApp(this)


        RemoteConfigurationService.initialize()

        if (!SpacesRepository.isSpacesConfigValid() && AuthService.isUserAuthenticated()) {
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
            firebaseDatabaseUtil.getSpacesConfigs()
        }

        if (AuthService.isUserAuthenticated()) {
            NumLikesRestriction.setDeviceLikeCounterAndDate(context = applicationContext) {}
        }

        ProcessLifecycleOwner.get().lifecycle.addObserver(billingClientLifecycle)

        // Branch logging for debugging
        if (BuildConfig.DEBUG) {
            Branch.enableTestMode()
            Branch.enableLogging()
        }

        // Branch object initialization
        Branch.getAutoInstance(this)

        // Initialize attribution tracking
        initializeAttributionTracking()
    }

    /**
     * Initialize attribution tracking for Google Ads and Meta Ads
     */
    private fun initializeAttributionTracking() {
        try {
            Timber.d("Initializing attribution tracking...")

            // Initialize Google Play Install Referrer for Google Ads attribution
            InstallReferrerHandler.initializeWithRetry(this, maxRetries = 3, retryDelayMs = 2000)

            // Initialize Meta Ads attribution with deferred deep links
            MetaAdsHandler.initializeWithRetry(this, maxRetries = 3, retryDelayMs = 3000)

            Timber.d("Attribution tracking initialization started")
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize attribution tracking")
        }
    }
}